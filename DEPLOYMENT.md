# Deploying Halo Opinions to Render

This guide explains how to deploy the Halo Opinions application to Render.

## Prerequisites

1. A [Render](https://render.com) account
2. A MongoDB database (you can use MongoDB Atlas)
3. Your code pushed to a Git repository (GitHub, GitLab, etc.)

## Deployment Options

There are two ways to deploy to Render:

1. **Using the render.yaml file** (recommended)
2. **Manual deployment** through the Render dashboard

## Option 1: Deploy using render.yaml

### Step 1: Connect your repository to Render

1. Log in to your Render account
2. Go to the Dashboard and click "New" > "Blueprint"
3. Connect your Git repository that contains the Halo Opinions code
4. Render will automatically detect the `render.yaml` file and configure the service

### Step 2: Configure environment variables

1. After the initial setup, go to your service in the Render dashboard
2. Click on "Environment" in the left sidebar
3. Add the following environment variables:
   - `MONGODB_URI`: Your MongoDB connection string
   - `JWT_SECRET`: Will be auto-generated
   - Other variables are already defined in the render.yaml file

### Step 3: Deploy

1. Click "Manual Deploy" > "Deploy latest commit"
2. <PERSON><PERSON> will build and deploy your application

## Option 2: Manual Deployment

### Step 1: Create a new Web Service

1. Log in to your Render account
2. Go to the Dashboard and click "New" > "Web Service"
3. Connect your Git repository
4. Configure the service:
   - **Name**: halo-opinions (or your preferred name)
   - **Environment**: Node
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm run server:prod`

### Step 2: Configure environment variables

1. Scroll down to the "Environment" section
2. Add the following environment variables:
   - `NODE_ENV`: production
   - `PORT`: 10000 (Render will automatically assign the actual port)
   - `MONGODB_URI`: Your MongoDB connection string
   - `JWT_SECRET`: A secure random string
   - `JWT_EXPIRES_IN`: 30d

### Step 3: Deploy

1. Click "Create Web Service"
2. Render will build and deploy your application

## Verifying Deployment

1. Once deployment is complete, Render will provide a URL for your application
2. Visit the URL to ensure the application is running correctly
3. Test the API endpoints using the `/api-docs` Swagger documentation

## Troubleshooting

If you encounter issues during deployment:

1. Check the Render logs for error messages
2. Ensure your MongoDB connection string is correct
3. Verify that all required environment variables are set
4. Check that the build and start commands are correct

## Updating Your Application

To update your application:

1. Push changes to your Git repository
2. Render will automatically rebuild and deploy the application

For manual updates, you can use the "Manual Deploy" button in the Render dashboard.

## Additional Resources

- [Render Documentation](https://render.com/docs)
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
