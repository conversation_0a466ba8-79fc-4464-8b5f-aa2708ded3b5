services:
  - type: web
    name: halo-opinions
    env: node
    buildCommand: npm install --production=false && npm run build-no-errors && cd server && npm install --production=true && cd ..
    startCommand: npm run server:prod
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: MONGODB_URI
        sync: false
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_EXPIRES_IN
        value: 30d
    healthCheckPath: /api/health
