import { Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "./components/ErrorBoundary";

// Lazy-loaded components for code splitting
const Home = lazy(() => import("./components/home"));
const NewSearchResults = lazy(() => import("./components/NewSearchResults"));
const ProductDetailPage = lazy(() => import("./pages/product/[id]"));
const NewProductDetailPage = lazy(() => import("./pages/NewProductDetailPage"));
const AdminPage = lazy(() => import("./pages/AdminPage"));
const ProductList = lazy(() => import("./components/ProductList"));
const ProductDetail = lazy(() => import("./pages/ProductDetail"));

// Loading fallback component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
  </div>
);

function App() {
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingFallback />}>
        {/* Main application routes */}
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/search" element={<NewSearchResults />} />
          <Route path="/products/id/:id" element={<NewProductDetailPage />} />
          <Route path="/products" element={<ProductList />} />
          <Route path="/products/:slug" element={<ProductDetail />} />
          <Route path="/admin" element={<AdminPage />} />
        </Routes>
        {/* Tempo routes removed for production */}
      </Suspense>

      {/* Toast notifications */}
      <Toaster />
    </ErrorBoundary>
  );
}

export default App;
