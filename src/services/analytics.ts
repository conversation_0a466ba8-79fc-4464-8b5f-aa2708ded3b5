/**
 * Analytics service for tracking user interactions
 */

import { v4 as uuidv4 } from 'uuid';

// Base URL for API requests - use relative URL in production
const API_BASE_URL = '/api';

// Session ID for anonymous tracking
let sessionId = localStorage.getItem('halo_session_id');
if (!sessionId) {
  sessionId = uuidv4();
  localStorage.setItem('halo_session_id', sessionId);
}

/**
 * Track a product click
 * @param productId - ID of the clicked product
 * @param referrer - Optional referrer information
 */
export const trackProductClick = async (productId: string, referrer?: string): Promise<void> => {
  try {
    // Don't block the UI with await
    fetch(`${API_BASE_URL}/analytics/product-click`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        productId,
        sessionId,
        referrer: referrer || document.referrer || window.location.pathname
      }),
      // Use keepalive to ensure the request completes even if the page is unloaded
      keepalive: true
    }).catch(error => {
      // Log error but don't throw - analytics should not break the app
      console.error('Error tracking product click:', error);
    });
  } catch (error) {
    // Log error but don't throw - analytics should not break the app
    console.error('Error preparing product click tracking:', error);
  }
};

/**
 * Track a product view (when user visits product detail page)
 * @param productId - ID of the viewed product
 * @param referrer - Optional referrer information
 */
export const trackProductView = async (productId: string, referrer?: string): Promise<void> => {
  try {
    // Don't block the UI with await
    fetch(`${API_BASE_URL}/analytics/product-view`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        productId,
        sessionId,
        referrer: referrer || document.referrer || window.location.pathname
      }),
      // Use keepalive to ensure the request completes even if the page is unloaded
      keepalive: true
    }).catch(error => {
      // Log error but don't throw - analytics should not break the app
      console.error('Error tracking product view:', error);
    });
  } catch (error) {
    // Log error but don't throw - analytics should not break the app
    console.error('Error preparing product view tracking:', error);
  }
};

/**
 * Get product click count (admin only)
 * @param productId - ID of the product
 * @returns Promise with click count data
 */
export const getProductClickCount = async (productId: string): Promise<{ productId: string; productName: string; clickCount: number }> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`${API_BASE_URL}/analytics/product-click/${productId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to get click count');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error getting product click count:', error);
    throw error;
  }
};

/**
 * Get trending products based on clicks (admin only)
 * @param days - Number of days to consider for trending calculation
 * @param limit - Number of products to return
 * @returns Promise with trending products data
 */
export const getTrendingByClicks = async (days = 7, limit = 10): Promise<Array<{ product: any; clickCount: number }>> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(
      `${API_BASE_URL}/analytics/trending-clicks?days=${days}&limit=${limit}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to get trending products');
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error getting trending products by clicks:', error);
    throw error;
  }
};
