/**
 * API service for communicating with the backend
 */

// Base URL for API requests - use relative URL in production
const API_BASE_URL = '/api';

// Log the API base URL for debugging
console.log('API Base URL:', API_BASE_URL);

// Cache for API responses
interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

const cache: Record<string, CacheEntry<any>> = {};

// Cache expiration time in milliseconds (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

// Autocomplete debounce delay in milliseconds (300ms)
const AUTOCOMPLETE_DEBOUNCE_DELAY = 300;

// Debounce function for autocomplete
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function (...args: Parameters<T>): void {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout !== null) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(later, wait);
  };
}

// Get data from cache or fetch from API
async function getFromCacheOrFetch<T>(
  cacheKey: string,
  fetchFn: () => Promise<T>,
  forceRefresh = false
): Promise<T> {
  // Always create a new cache key with timestamp to avoid collisions
  const fullCacheKey = `${cacheKey}`;

  // Check if data is in cache and not expired
  const cachedData = cache[fullCacheKey];
  const now = Date.now();

  if (!forceRefresh && cachedData && now - cachedData.timestamp < CACHE_EXPIRATION) {
    console.log(`Using cached data for ${fullCacheKey}`);
    return cachedData.data;
  }

  // Fetch fresh data
  console.log(`Fetching fresh data for ${fullCacheKey}`);
  const data = await fetchFn();

  // Update cache
  cache[fullCacheKey] = {
    data,
    timestamp: now
  };

  return data;
}

// Types
export interface Product {
  _id?: string; // MongoDB ID
  id?: string; // Fallback ID
  name: string;
  description: string;
  imageUrls: string[];
  platforms: Platform[];
  sentiment: Sentiment;
  aggregateScore: number;
  categories?: string[];
  createdAt?: string;
  updatedAt?: string;
  reviews?: Review[];
}

export interface Platform {
  name: string;
  productUrl: string;
  price: number;
  rating: number;
  reviewCount: number;
  sellerReputation?: number;
}

export interface Sentiment {
  positive: number;
  neutral: number;
  negative: number;
}

export interface Review {
  rating: number;
  title?: string;
  body: string;
  date?: string;
  author?: string;
  platform: string;
  verified?: boolean;
}

export interface SearchParams {
  q?: string;
  category?: string;
  platform?: string;
  minPrice?: number;
  maxPrice?: number;
  minRating?: number;
  page?: number;
  limit?: number;
  sort?: string;
}

export interface AutocompleteSuggestion {
  id: string;
  name: string;
  image: string | null;
  platforms: string[];
  lowestPrice: number;
}

export interface AutocompleteResponse {
  suggestions: AutocompleteSuggestion[];
}

export interface SearchResponse {
  products: Product[];
  page: number;
  totalPages: number;
  total: number;
}

export interface PriceResponse {
  prices: {
    platform: string;
    price: number;
    productUrl: string;
    sellerReputation?: number;
    rating?: number;
  }[];
}

export interface ReviewResponse {
  reviews: {
    platform: string;
    rating: number;
    reviewCount: number;
    body: string;
    author?: string;
    date?: string;
    verified?: boolean;
  }[];
  sentiment: Sentiment;
  sentimentAnalysis?: Sentiment;
  scrapedReviews?: Review[];
}

/**
 * Search for products
 * @param params Search parameters
 * @returns Promise with search results
 */
export const searchProducts = async (params: SearchParams, forceRefresh = false): Promise<SearchResponse> => {
  // Build query string from params
  const queryParams = new URLSearchParams();

  if (params.q) queryParams.append('q', params.q);
  if (params.category) queryParams.append('category', params.category);
  if (params.platform) queryParams.append('platform', params.platform);
  if (params.minPrice) queryParams.append('minPrice', params.minPrice.toString());
  if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice.toString());
  if (params.minRating) queryParams.append('minRating', params.minRating.toString());
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());
  if (params.sort) queryParams.append('sort', params.sort);

  const queryString = queryParams.toString();
  const cacheKey = `search:${queryString}`;

  return getFromCacheOrFetch<SearchResponse>(
    cacheKey,
    async () => {
      try {
        const url = `${API_BASE_URL}/search?${queryString}`;
        console.log('Fetching from URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response text:', errorText);

          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            console.error('Failed to parse error response as JSON:', e);
            throw new Error(`Failed to search products: ${response.status} ${response.statusText}`);
          }

          throw new Error(errorData.message || 'Failed to search products');
        }

        const data = await response.json();
        console.log('Received data:', data);
        return data;
      } catch (error) {
        console.error('Error searching products:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Get product details by ID
 * @param id Product ID
 * @returns Promise with product details
 */
export const getProductById = async (id: string, forceRefresh = false): Promise<Product> => {
  const cacheKey = `product:${id}`;

  return getFromCacheOrFetch<Product>(
    cacheKey,
    async () => {
      try {
        console.log(`Fetching product with ID: ${id}`);
        const url = `${API_BASE_URL}/products/${id}`;
        console.log('Request URL:', url);

        const response = await fetch(url);
        console.log('Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response text:', errorText);

          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            console.error('Failed to parse error response as JSON:', e);
            throw new Error(`Failed to get product details: ${response.status} ${response.statusText}`);
          }

          throw new Error(errorData.message || 'Failed to get product details');
        }

        const data = await response.json();
        console.log('Received product data:', data);
        return data;
      } catch (error) {
        console.error('Error getting product details:', error);
        throw error;
      }
    },
    forceRefresh
  );
};

/**
 * Get product reviews
 * @param id Product ID
 * @param refresh Whether to refresh reviews from source
 * @returns Promise with reviews and sentiment
 */
export const getProductReviews = async (id: string, refresh: boolean = false): Promise<ReviewResponse> => {
  // If refresh is true, we'll force a refresh of the cache
  const cacheKey = `reviews:${id}`;

  return getFromCacheOrFetch<ReviewResponse>(
    cacheKey,
    async () => {
      try {
        const queryParams = new URLSearchParams();
        if (refresh) queryParams.append('refresh', 'true');

        const url = `${API_BASE_URL}/products/${id}/reviews?${queryParams.toString()}`;
        console.log('Fetching reviews from URL:', url);

        const response = await fetch(url);
        console.log('Reviews response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response text:', errorText);

          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            console.error('Failed to parse error response as JSON:', e);
            throw new Error(`Failed to get product reviews: ${response.status} ${response.statusText}`);
          }

          throw new Error(errorData.message || 'Failed to get product reviews');
        }

        const data = await response.json();
        console.log('Received reviews data:', data);
        return data;
      } catch (error) {
        console.error('Error getting product reviews:', error);
        throw error;
      }
    },
    refresh // Use refresh parameter to force cache refresh
  );
};

/**
 * Get product prices
 * @param id Product ID
 * @param refresh Whether to refresh prices from source
 * @returns Promise with prices
 */
export const getProductPrices = async (id: string, refresh: boolean = false): Promise<PriceResponse> => {
  // If refresh is true, we'll force a refresh of the cache
  const cacheKey = `prices:${id}`;

  return getFromCacheOrFetch<PriceResponse>(
    cacheKey,
    async () => {
      try {
        const queryParams = new URLSearchParams();
        if (refresh) queryParams.append('refresh', 'true');

        const url = `${API_BASE_URL}/products/${id}/prices?${queryParams.toString()}`;
        console.log('Fetching prices from URL:', url);

        const response = await fetch(url);
        console.log('Prices response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response text:', errorText);

          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            console.error('Failed to parse error response as JSON:', e);
            throw new Error(`Failed to get product prices: ${response.status} ${response.statusText}`);
          }

          throw new Error(errorData.message || 'Failed to get product prices');
        }

        const data = await response.json();
        console.log('Received prices data:', data);
        return data;
      } catch (error) {
        console.error('Error getting product prices:', error);
        throw error;
      }
    },
    refresh // Use refresh parameter to force cache refresh
  );
};

/**
 * Get autocomplete suggestions for a search query
 * @param {string} query - The search query
 * @returns {Promise<AutocompleteResponse>} - Autocomplete suggestions
 */
export const getAutocompleteSuggestions = async (query: string): Promise<AutocompleteResponse> => {
  if (!query || query.length < 2) {
    return { suggestions: [] };
  }

  try {
    const url = `${API_BASE_URL}/search/autocomplete?q=${encodeURIComponent(query)}`;
    console.log('Fetching autocomplete suggestions from URL:', url);

    const response = await fetch(url);
    console.log('Autocomplete response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response text:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (e) {
        console.error('Failed to parse error response as JSON:', e);
        throw new Error(`Failed to get autocomplete suggestions: ${response.status} ${response.statusText}`);
      }

      throw new Error(errorData.message || 'Failed to get autocomplete suggestions');
    }

    const data = await response.json();
    console.log('Received autocomplete data:', data);
    return data;
  } catch (error) {
    console.error('Error getting autocomplete suggestions:', error);
    throw error;
  }
};

/**
 * Debounced version of getAutocompleteSuggestions
 * @param {string} query - The search query
 * @param {Function} callback - Callback function to handle the results
 */
export const debouncedGetAutocompleteSuggestions = debounce(
  async (query: string, callback: (data: AutocompleteResponse) => void) => {
    try {
      const data = await getAutocompleteSuggestions(query);
      callback(data);
    } catch (error) {
      console.error('Error in debounced autocomplete:', error);
      callback({ suggestions: [] });
    }
  },
  AUTOCOMPLETE_DEBOUNCE_DELAY
);


