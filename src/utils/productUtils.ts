import { getProductBySlug, getProductById, clearProductCache } from '@/services/newApi';

/**
 * Safely navigate to a product page by checking if the product exists first
 * @param navigate React Router navigate function
 * @param productId Product ID
 * @param productSlug Product slug
 */
export const safeNavigateToProduct = async (
  navigate: (path: string) => void,
  productId: string,
  productSlug: string
): Promise<void> => {
  try {
    // Clear any existing cache for this product
    clearProductCache(productId);

    // Try to fetch the product by slug to verify it exists
    await getProductBySlug(productSlug, true);

    // If successful, navigate to the product page
    navigate(`/products/${productSlug}`);
  } catch (error) {
    console.error(`Error verifying product existence: ${error}`);

    // If the slug-based fetch fails, try by ID as fallback
    try {
      await getProductById(productId, true);
      navigate(`/products/id/${productId}`);
    } catch (secondError) {
      console.error(`Fallback navigation also failed: ${secondError}`);

      // If both fail, clear all product cache and navigate to search
      clearProductCache();
      navigate('/search');
    }
  }
};
