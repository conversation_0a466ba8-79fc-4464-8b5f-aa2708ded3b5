/**
 * CSRF token utilities for secure API requests
 */

// Get CSRF token from cookies
export const getCsrfToken = (): string | null => {
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'XSRF-TOKEN') {
      return decodeURIComponent(value);
    }
  }
  return null;
};

// Store CSRF token from API response
export const storeCsrfToken = (response: any): void => {
  if (response && response.csrfToken) {
    document.cookie = `XSRF-TOKEN=${response.csrfToken}; path=/; SameSite=Strict; ${
      window.location.protocol === 'https:' ? 'Secure;' : ''
    }`;
  }
};

// Add CSRF token to request headers
export const addCsrfHeader = (headers: HeadersInit = {}): HeadersInit => {
  const token = getCsrfToken();
  const newHeaders: Record<string, string> = { ...headers as Record<string, string> };
  
  if (token) {
    newHeaders['X-CSRF-Token'] = token;
  }
  
  return newHeaders;
};

// Create fetch options with CSRF token
export const createFetchOptions = (
  method: string = 'GET',
  body?: any,
  additionalHeaders: Record<string, string> = {}
): RequestInit => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...additionalHeaders
  };
  
  // Add CSRF token to headers for non-GET requests
  if (method !== 'GET') {
    const csrfHeaders = addCsrfHeader(headers);
    Object.assign(headers, csrfHeaders);
  }
  
  const options: RequestInit = {
    method,
    headers,
    credentials: 'include' // Important for cookies
  };
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  return options;
};

// Enhanced fetch with CSRF protection
export const fetchWithCsrf = async (
  url: string,
  method: string = 'GET',
  body?: any,
  additionalHeaders: Record<string, string> = {}
): Promise<any> => {
  const options = createFetchOptions(method, body, additionalHeaders);
  
  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => null);
    throw new Error(errorData?.message || `Request failed with status ${response.status}`);
  }
  
  const data = await response.json();
  
  // Store CSRF token from response if available
  storeCsrfToken(data);
  
  return data;
};
