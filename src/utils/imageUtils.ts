/**
 * Return the original image URL without optimization
 * Image optimization has been disabled
 * @param originalUrl Original image URL
 * @returns Original image URL
 */
export const getOptimizedImageUrl = (
  originalUrl: string,
  width: number = 800,
  quality: number = 80,
  format: 'webp' | 'jpeg' | 'png' | 'avif' = 'webp'
): string => {
  return originalUrl || '';
};

/**
 * Return the original image URL without generating srcset
 * Image optimization has been disabled
 * @param originalUrl Original image URL
 * @returns Original image URL
 */
export const generateSrcSet = (
  originalUrl: string,
  widths: number[] = [400, 800, 1200],
  quality: number = 80,
  format: 'webp' | 'jpeg' | 'png' | 'avif' = 'webp'
): string => {
  // Just return the original URL since image optimization is disabled
  return originalUrl || '';
};

/**
 * Get appropriate sizes attribute for responsive images
 * @param breakpoints Object with breakpoint sizes
 * @returns Sizes attribute string
 */
export const getResponsiveSizes = (
  breakpoints: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    default: number
  }
): string => {
  const sizes = [];

  if (breakpoints.sm) {
    sizes.push(`(max-width: 640px) ${breakpoints.sm}px`);
  }

  if (breakpoints.md) {
    sizes.push(`(max-width: 768px) ${breakpoints.md}px`);
  }

  if (breakpoints.lg) {
    sizes.push(`(max-width: 1024px) ${breakpoints.lg}px`);
  }

  if (breakpoints.xl) {
    sizes.push(`(max-width: 1280px) ${breakpoints.xl}px`);
  }

  sizes.push(`${breakpoints.default}px`);

  return sizes.join(', ');
};
