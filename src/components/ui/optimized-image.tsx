import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  placeholderColor?: string;
  loadingClassName?: string;
  fallbackSrc?: string;
  sizes?: string;
}

/**
 * OptimizedImage component with lazy loading and responsive sizing
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  placeholderColor = '#f3f4f6',
  loadingClassName,
  fallbackSrc,
  sizes = '100vw',
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observer = useRef<IntersectionObserver | null>(null);

  // Generate srcset for responsive images
  const generateSrcSet = (url: string): string => {
    // Skip for data URLs or SVGs
    if (url.startsWith('data:') || url.endsWith('.svg')) {
      return url;
    }

    // For external images that support width parameters
    if (url.includes('cloudinary.com')) {
      return [
        `${url.replace('/upload/', '/upload/w_400/')} 400w`,
        `${url.replace('/upload/', '/upload/w_800/')} 800w`,
        `${url.replace('/upload/', '/upload/w_1200/')} 1200w`,
      ].join(', ');
    }

    // For images that don't support width parameters, return the original
    return url;
  };

  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
  };

  // Handle image error
  const handleError = () => {
    setError(true);
    if (fallbackSrc) {
      // If there's a fallback image, use it
      if (imgRef.current) {
        imgRef.current.src = fallbackSrc;
      }
    }
  };

  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (!imgRef.current) return;

    observer.current = new IntersectionObserver((entries) => {
      const [entry] = entries;
      if (entry.isIntersecting) {
        // When image is in viewport, set the src attribute to load the image
        if (imgRef.current) {
          imgRef.current.src = src;
          if (imgRef.current.complete) {
            setIsLoaded(true);
          }
        }
        // Disconnect observer after loading
        observer.current?.disconnect();
      }
    }, {
      rootMargin: '200px', // Start loading when image is 200px from viewport
      threshold: 0
    });

    observer.current.observe(imgRef.current);

    return () => {
      observer.current?.disconnect();
    };
  }, [src]);

  // Calculate aspect ratio for placeholder
  const aspectRatio = height && width ? (height / width) : undefined;
  const paddingBottom = aspectRatio ? `${aspectRatio * 100}%` : undefined;

  return (
    <div
      className={cn(
        'relative overflow-hidden',
        className
      )}
      style={{
        paddingBottom,
        backgroundColor: placeholderColor,
        width: width ? `${width}px` : '100%',
        height: height ? `${height}px` : 'auto',
      }}
    >
      <img
        ref={imgRef}
        alt={alt}
        data-src={src} // Store the real src here
        srcSet="" // Will be set by intersection observer
        sizes={sizes}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          'transition-opacity duration-300 w-full h-full object-cover',
          isLoaded ? 'opacity-100' : 'opacity-0',
          error ? 'bg-gray-200' : '',
          loadingClassName
        )}
        {...props}
      />
      
      {!isLoaded && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-pulse w-full h-full bg-gray-200"></div>
        </div>
      )}
      
      {error && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <span className="text-gray-400 text-sm">Image not available</span>
        </div>
      )}
    </div>
  );
};

export { OptimizedImage };
