import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Check, Filter, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getProductReviews, type Review as ApiReview } from "@/services/api";
import ReviewSentimentAnalysis from "./ReviewSentimentAnalysis";

interface Review {
  id: string;
  source: {
    name: string;
    logo: string;
  };
  verified: boolean;
  rating: number;
  date: string;
  reviewer: {
    name: string;
    avatar?: string;
  };
  text: string;
}

interface ReviewListWithAPIProps {
  productId: string;
  limit?: number;
}

// Platform logos
const platformLogos: Record<string, string> = {
  'Amazon': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/2560px-Amazon_logo.svg.png',
  'Walmart': 'https://cdn.worldvectorlogo.com/logos/walmart.svg',
  'eBay': 'https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/EBay_logo.svg/2560px-EBay_logo.svg.png',
  'Target': 'https://upload.wikimedia.org/wikipedia/commons/thumb/c/c5/Target_Corporation_logo_%28vector%29.svg/1200px-Target_Corporation_logo_%28vector%29.svg.png',
  'Best Buy': 'https://upload.wikimedia.org/wikipedia/commons/thumb/f/f5/Best_Buy_Logo.svg/1280px-Best_Buy_Logo.svg.png',
  'Jumia': 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e0/Jumia_Logo.png/1200px-Jumia_Logo.png'
};

// Convert API review to UI review
const mapApiReviewToUiReview = (apiReview: ApiReview, index: number): Review => {
  return {
    id: `review-${index}`,
    source: {
      name: apiReview.platform,
      logo: platformLogos[apiReview.platform] || 'https://via.placeholder.com/50'
    },
    verified: apiReview.verified || false,
    rating: apiReview.rating,
    date: apiReview.date || new Date().toISOString().split('T')[0],
    reviewer: {
      name: apiReview.author || 'Anonymous User'
    },
    text: apiReview.body
  };
};

const ReviewListWithAPI = ({ productId, limit }: ReviewListWithAPIProps) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState("all");
  const [sortBy, setSortBy] = useState("recent");
  const [refreshing, setRefreshing] = useState(false);
  const [sentimentAnalysis, setSentimentAnalysis] = useState<any>(null);

  // Fetch reviews
  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log(`Fetching reviews for product ID: ${productId}`);
        const response = await getProductReviews(productId, false);
        console.log('Review response:', response);

        // Set sentiment analysis if available
        if (response.sentimentAnalysis) {
          console.log('Sentiment analysis:', response.sentimentAnalysis);
          setSentimentAnalysis(response.sentimentAnalysis);
        }

        if (response.reviews && response.reviews.length > 0) {
          // Map API reviews to UI reviews
          console.log(`Found ${response.reviews.length} reviews`);
          const mappedReviews = response.reviews.map(mapApiReviewToUiReview);
          setReviews(mappedReviews);
        } else {
          console.log('No reviews found for this product');
          setReviews([]);
          setError('No reviews available for this product yet.');
        }
      } catch (err) {
        console.error('Error fetching reviews:', err);
        setError('Failed to load reviews. Please try again.');
        setReviews([]);
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [productId]);

  // Refresh reviews
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);

      console.log(`Refreshing reviews for product ID: ${productId}`);
      const response = await getProductReviews(productId, true);
      console.log('Refreshed review response:', response);

      // Set sentiment analysis if available
      if (response.sentimentAnalysis) {
        console.log('Refreshed sentiment analysis:', response.sentimentAnalysis);
        setSentimentAnalysis(response.sentimentAnalysis);
      }

      if (response.reviews && response.reviews.length > 0) {
        // Map API reviews to UI reviews
        console.log(`Found ${response.reviews.length} refreshed reviews`);
        const mappedReviews = response.reviews.map(mapApiReviewToUiReview);
        setReviews(mappedReviews);
      } else {
        console.log('No refreshed reviews found');
        setReviews([]);
        setError('No reviews available for this product yet.');
      }
    } catch (err) {
      console.error('Error refreshing reviews:', err);
      setError('Failed to refresh reviews. Please try again.');
    } finally {
      setRefreshing(false);
    }
  };

  // Filter reviews based on selected filter
  const filteredReviews = reviews.filter((review) => {
    if (filter === "all") return true;
    if (filter === "verified") return review.verified;
    if (filter === "5star") return review.rating === 5;
    if (filter === "4star") return review.rating === 4;
    if (filter === "3star") return review.rating === 3;
    if (filter === "2star") return review.rating === 2;
    if (filter === "1star") return review.rating === 1;
    return true;
  });

  // Sort reviews based on selected sort option
  const sortedReviews = [...filteredReviews].sort((a, b) => {
    if (sortBy === "recent") {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    }
    if (sortBy === "highest") {
      return b.rating - a.rating;
    }
    if (sortBy === "lowest") {
      return a.rating - b.rating;
    }
    return 0;
  });

  // Loading state
  if (loading) {
    return (
      <div className="w-full bg-white p-4 rounded-lg flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading reviews...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white p-4 rounded-lg">
      {/* Sentiment Analysis */}
      {sentimentAnalysis && (
        <div className="mb-6">
          <ReviewSentimentAnalysis sentimentAnalysis={sentimentAnalysis} />
        </div>
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-2xl font-bold">Customer Opinions</h2>

        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter opinions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Opinions</SelectItem>
                <SelectItem value="verified">Verified Only</SelectItem>
                <SelectItem value="5star">5 Star</SelectItem>
                <SelectItem value="4star">4 Star</SelectItem>
                <SelectItem value="3star">3 Star</SelectItem>
                <SelectItem value="2star">2 Star</SelectItem>
                <SelectItem value="1star">1 Star</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="highest">Highest Rating</SelectItem>
                <SelectItem value="lowest">Lowest Rating</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            {refreshing && <Loader2 className="h-4 w-4 animate-spin" />}
            {refreshing ? 'Refreshing...' : 'Refresh Opinions'}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {sortedReviews.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-gray-500">
            No opinions match your filter criteria.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {(limit ? sortedReviews.slice(0, limit) : sortedReviews).map(
            (review) => (
              <Card key={review.id} className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage
                          src={
                            review.reviewer.avatar ||
                            `https://api.dicebear.com/7.x/avataaars/svg?seed=${review.reviewer.name}`
                          }
                          alt={review.reviewer.name}
                        />
                        <AvatarFallback>
                          {review.reviewer.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">
                          {review.reviewer.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(review.date).toLocaleDateString()}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col items-end gap-1">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < review.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-300"}`}
                          />
                        ))}
                      </div>
                      <div className="flex items-center gap-2">
                        <img
                          src={review.source.logo}
                          alt={review.source.name}
                          className="h-5 w-5 object-contain"
                        />
                        <span className="text-xs text-gray-500">
                          {review.source.name}
                        </span>
                        {review.verified && (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1 text-green-600 border-green-600"
                          >
                            <Check className="h-3 w-3" />
                            <span className="text-xs">Verified</span>
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <p className="text-gray-700">{review.text}</p>
                  </div>
                </CardContent>
              </Card>
            ),
          )}
        </div>
      )}

      {sortedReviews.length > 5 && (
        <Pagination className="mt-6">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious href="#" />
            </PaginationItem>
            <PaginationItem>
              <PaginationLink href="#" isActive>
                1
              </PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationLink href="#">2</PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationLink href="#">3</PaginationLink>
            </PaginationItem>
            <PaginationItem>
              <PaginationNext href="#" />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
};

// Mock data for development
const mockReviews: Review[] = [
  {
    id: "1",
    source: {
      name: "Amazon",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/2560px-Amazon_logo.svg.png",
    },
    verified: true,
    rating: 5,
    date: "2023-05-15",
    reviewer: {
      name: "John Smith",
    },
    text: "This product exceeded my expectations! The quality is outstanding and it arrived earlier than expected. Would definitely recommend to anyone looking for a reliable solution.",
  },
  {
    id: "2",
    source: {
      name: "Walmart",
      logo: "https://cdn.worldvectorlogo.com/logos/walmart.svg",
    },
    verified: true,
    rating: 4,
    date: "2023-06-20",
    reviewer: {
      name: "Sarah Johnson",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah",
    },
    text: "Great product for the price. The only reason I'm giving 4 stars instead of 5 is because the color was slightly different than what was shown in the pictures. Otherwise, it works perfectly.",
  },
  {
    id: "3",
    source: {
      name: "eBay",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1b/EBay_logo.svg/2560px-EBay_logo.svg.png",
    },
    verified: false,
    rating: 3,
    date: "2023-07-05",
    reviewer: {
      name: "Michael Brown",
    },
    text: "It's an okay product. Does what it's supposed to do but nothing extraordinary. Shipping was fast though, which was nice.",
  }
];

export default ReviewListWithAPI;
