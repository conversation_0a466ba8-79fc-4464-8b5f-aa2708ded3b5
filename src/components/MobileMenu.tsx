import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

interface MobileMenuProps {
  className?: string;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ className = "" }) => {
  const [open, setOpen] = React.useState(false);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild className={className}>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[80%] sm:w-[350px] pt-10">
        <nav className="flex flex-col gap-6">
          <Link 
            to="/" 
            className="text-lg font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Home
          </Link>
          <Link 
            to="/products" 
            className="text-lg font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Products
          </Link>
          <Link 
            to="#" 
            className="text-lg font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            About
          </Link>
          <Link 
            to="#" 
            className="text-lg font-medium hover:text-primary"
            onClick={() => setOpen(false)}
          >
            Contact
          </Link>
        </nav>
      </SheetContent>
    </Sheet>
  );
};

export default MobileMenu;
