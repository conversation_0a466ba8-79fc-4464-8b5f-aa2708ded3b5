import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { RefreshCw, Home } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

// Error boundary class component
class ErrorBoundaryClass extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
  }

  public render(): ReactNode {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}

// Error fallback component
interface ErrorFallbackProps {
  error: Error | null;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error }) => {
  const handleRefresh = () => {
    window.location.reload();
  };
  
  // We need to use a wrapper since useNavigate can't be used directly in class components
  return <ErrorFallbackWithNavigation error={error} />;
};

// Wrapper to use hooks
const ErrorFallbackWithNavigation: React.FC<ErrorFallbackProps> = ({ error }) => {
  const navigate = useNavigate();
  
  const handleGoHome = () => {
    navigate('/');
  };
  
  const handleRefresh = () => {
    window.location.reload();
  };
  
  return (
    <div className="flex items-center justify-center min-h-[50vh] p-6">
      <div className="w-full max-w-md">
        <Alert variant="destructive" className="mb-4">
          <AlertTitle className="text-lg font-semibold mb-2">
            Something went wrong
          </AlertTitle>
          <AlertDescription className="mt-2">
            <div className="text-sm mb-4">
              {error?.message || 'An unexpected error occurred'}
            </div>
            <div className="flex space-x-2 mt-4">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                className="flex items-center"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Page
              </Button>
              <Button 
                variant="default" 
                size="sm" 
                onClick={handleGoHome}
                className="flex items-center"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </div>
  );
};

// Export the error boundary
export default ErrorBoundaryClass;
