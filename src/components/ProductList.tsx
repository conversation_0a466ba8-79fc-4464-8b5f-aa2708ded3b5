import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getProducts, getCategories } from '../services/newApi';
import { trackProductClick } from '@/services/analytics';
import SimpleSearchAutocomplete from './SimpleSearchAutocomplete';
import ProductCard from './ProductCard';
import Header from './Header';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Loader2, Filter } from 'lucide-react';
import { Alert, AlertDescription } from './ui/alert';

interface ProductListProps {
  title?: string;
  showFilters?: boolean;
}

const ProductList: React.FC<ProductListProps> = ({
  title = 'Product Reviews',
  showFilters = true
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get('page') || '1', 10));
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);

  // Filters
  const [category, setCategory] = useState(searchParams.get('category') || 'all');
  const [brand, setBrand] = useState(searchParams.get('brand') || 'all');
  const [minScore, setMinScore] = useState(searchParams.get('minScore') || '0');
  const [sortOption, setSortOption] = useState(searchParams.get('sort') || '-createdAt');

  // Categories and brands for filters
  const [categories, setCategories] = useState<string[]>([]);
  const [brands, setBrands] = useState<string[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  useEffect(() => {
    fetchProducts();
  }, [currentPage, category, brand, minScore, sortOption]);

  useEffect(() => {
    // Extract unique brands from products
    const uniqueBrands = [...new Set(products.map(p => p.brand))];
    setBrands(uniqueBrands);
  }, [products]);

  // Fetch categories when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      setLoadingCategories(true);
      try {
        const categoriesData = await getCategories();
        setCategories(categoriesData);
      } catch (err) {
        console.error('Error fetching categories:', err);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  const fetchProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      // Update URL params
      const params: any = { page: currentPage };
      if (category && category !== 'all') params.category = category;
      if (brand && brand !== 'all') params.brand = brand;
      if (minScore && minScore !== '0') params.minScore = minScore;
      if (sortOption) params.sort = sortOption;

      setSearchParams(params);

      // Fetch products
      const response = await getProducts({
        page: currentPage,
        limit: 8,
        category: category && category !== 'all' ? category : undefined,
        brand: brand && brand !== 'all' ? brand : undefined,
        minScore: minScore && minScore !== '0' ? parseInt(minScore) : undefined,
        sort: sortOption
      });

      setProducts(response.data);
      setTotalPages(Math.ceil(response.total / 8));
      setTotalProducts(response.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch products');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleProductClick = useCallback((product: any) => {
    // Track the product click
    trackProductClick(product._id);

    // Navigate to the product page
    navigate(`/products/${product.slug}`);
  }, [navigate]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleFilterChange = () => {
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setCategory('all');
    setBrand('all');
    setMinScore('0');
    setSortOption('-createdAt');
    setCurrentPage(1);
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    return (
      <Pagination className="mt-8">
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
              className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
            />
          </PaginationItem>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
            <PaginationItem key={page}>
              <PaginationLink
                onClick={() => handlePageChange(page)}
                isActive={currentPage === page}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          ))}

          <PaginationItem>
            <PaginationNext
              onClick={() => handlePageChange(Math.min(currentPage + 1, totalPages))}
              className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold">{title}</h1>
            {totalProducts > 0 && <p className="text-sm text-muted-foreground">{totalProducts} products found</p>}
          </div>

          <div className="w-full md:w-auto min-w-[300px]">
            <SimpleSearchAutocomplete
              placeholder="Search products..."
              onSearch={(query) => {
                navigate(`/search?q=${encodeURIComponent(query)}`);
              }}
            />
          </div>
        </div>

        {showFilters && (
          <div className="mb-8 p-4 bg-muted/50 rounded-lg">
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="flex-1">
                <label className="block text-sm font-medium mb-1">Category</label>
                <Select value={category} onValueChange={(value) => { setCategory(value); handleFilterChange(); }}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    <SelectItem value="all">All Categories</SelectItem>
                    {loadingCategories ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" /> Loading...
                      </div>
                    ) : categories.length > 0 ? (
                      categories.map(cat => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-center text-muted-foreground">
                        No categories found
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1">
                <label className="block text-sm font-medium mb-1">Brand</label>
                <Select value={brand} onValueChange={(value) => { setBrand(value); handleFilterChange(); }}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Brands" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Brands</SelectItem>
                    {brands.map(b => (
                      <SelectItem key={b} value={b}>{b}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1">
                <label className="block text-sm font-medium mb-1">Minimum Score</label>
                <Select value={minScore} onValueChange={(value) => { setMinScore(value); handleFilterChange(); }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any Score" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Any Score</SelectItem>
                    <SelectItem value="90">90% and above</SelectItem>
                    <SelectItem value="80">80% and above</SelectItem>
                    <SelectItem value="70">70% and above</SelectItem>
                    <SelectItem value="60">60% and above</SelectItem>
                    <SelectItem value="50">50% and above</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex-1">
                <label className="block text-sm font-medium mb-1">Sort By</label>
                <Select value={sortOption} onValueChange={(value) => { setSortOption(value); handleFilterChange(); }}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="-createdAt">Newest First</SelectItem>
                    <SelectItem value="createdAt">Oldest First</SelectItem>
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="-name">Name (Z-A)</SelectItem>
                    <SelectItem value="price">Price (Low to High)</SelectItem>
                    <SelectItem value="-price">Price (High to Low)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end">
              <Button variant="outline" size="sm" onClick={handleClearFilters}>
                <Filter className="mr-2 h-4 w-4" /> Clear Filters
              </Button>
            </div>
          </div>
        )}

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="flex justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">No products found</p>
            {showFilters && (
              <Button variant="outline" className="mt-4" onClick={handleClearFilters}>
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map(product => (
              <ProductCard
                key={product._id}
                id={product._id}
                slug={product.slug}
                name={product.name}
                image={product.image}
                rating={4.5} // Default rating since we don't have this in our new schema
                reviewCount={0} // Default review count
                sources={[product.brand]}
                score={product.score}
                onClick={() => handleProductClick(product)}
              />
            ))}
          </div>
        )}

        {renderPagination()}
      </div>
    </div>
  );
};

export default ProductList;
