import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { AutocompleteSuggestion } from '@/services/api';
import { debouncedGetAutocompleteSuggestions } from '@/services/api';

interface AutocompleteSearchProps {
  placeholder?: string;
  className?: string;
  onSearch?: (query: string) => void;
  onQueryChange?: (query: string) => void;
  initialQuery?: string;
}

const AutocompleteSearch: React.FC<AutocompleteSearchProps> = ({
  placeholder = 'Search for products...',
  className = '',
  onSearch,
  onQueryChange,
  initialQuery = '',
}) => {
  const [query, setQuery] = useState(initialQuery);
  const [suggestions, setSuggestions] = useState<AutocompleteSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);

    // Call the onQueryChange callback if provided
    if (onQueryChange) {
      onQueryChange(value);
    }

    if (value.length >= 2) {
      setIsLoading(true);
      debouncedGetAutocompleteSuggestions(value, (data) => {
        setSuggestions(data.suggestions);
        setIsLoading(false);
        setShowSuggestions(true);
      });
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle search submission
  const handleSubmit = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.preventDefault();
    if (query.trim()) {
      // Use the onSearch callback if provided, otherwise navigate directly
      if (onSearch) {
        onSearch(query.trim());
      } else {
        navigate(`/search?q=${encodeURIComponent(query.trim())}`);
      }
      setShowSuggestions(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: AutocompleteSuggestion) => {
    navigate(`/product/${suggestion.id}`);
    setShowSuggestions(false);
    setQuery('');
  };

  // Handle clear button click
  const handleClear = () => {
    setQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <div className="relative">
          <Input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            placeholder={placeholder}
            className="pr-16 pl-4"
            onFocus={() => query.length >= 2 && setSuggestions.length > 0 && setShowSuggestions(true)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleSubmit(e as any);
              }
            }}
          />
          {query && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-10 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={handleClear}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
          <Button
            type="button"
            size="sm"
            variant="ghost"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={(e) => handleSubmit(e as any)}
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 mt-1 w-full bg-background border rounded-md shadow-lg max-h-60 overflow-auto"
        >
          <ul className="py-1">
            {suggestions.map((suggestion) => (
              <li
                key={suggestion.id}
                className="px-4 py-2 hover:bg-muted cursor-pointer flex items-center"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                {suggestion.image && (
                  <img
                    src={suggestion.image}
                    alt={suggestion.name}
                    className="w-10 h-10 object-cover mr-3 rounded"
                  />
                )}
                <div className="flex-1">
                  <div className="font-medium">{suggestion.name}</div>
                  <div className="text-sm text-muted-foreground flex items-center">
                    <span className="mr-2">${suggestion.lowestPrice.toFixed(2)}</span>
                    <span className="text-xs">
                      {suggestion.platforms.slice(0, 3).join(', ')}
                      {suggestion.platforms.length > 3 && '...'}
                    </span>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && query.length >= 2 && (
        <div className="absolute z-50 mt-1 w-full bg-background border rounded-md shadow-lg p-4 text-center">
          <div className="animate-pulse">Loading suggestions...</div>
        </div>
      )}
    </div>
  );
};

export default AutocompleteSearch;
