import { Card, CardContent } from "./ui/card";
import { Star } from "lucide-react";
import { useNavigate } from "react-router-dom";
import Header from "./Header";
import SimpleSearchAutocomplete from "./SimpleSearchAutocomplete";
import ProductCategoriesGrid from "./ProductCategoriesGrid";
import CommunityCallToAction from "./CommunityCallToAction";

const Home = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-teal-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div className="absolute top-0 right-0 w-96 h-96 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-96 h-96 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-gray-900">
            Find honest product opinions
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto">
            Discover, redirect and avoid scams.
          </p>

          {/* Search Form */}
          <div className="max-w-2xl mx-auto">
            <SimpleSearchAutocomplete
              placeholder="Search for a product..."
              className="w-full"
              onSearch={(query) => {
                if (query.trim()) {
                  navigate(`/search?q=${encodeURIComponent(query)}`);
                }
              }}
            />

            {/* Popular searches */}
            <div className="mt-6 flex flex-wrap justify-center gap-2">
              <span className="text-sm text-gray-500">Popular:</span>
              {['iPhone 15', 'MacBook Pro', 'AirPods', 'Samsung TV'].map((term) => (
                <button
                  key={term}
                  onClick={() => navigate(`/search?q=${encodeURIComponent(term)}`)}
                  className="text-sm bg-white hover:bg-gray-50 text-gray-700 px-3 py-1 rounded-full border border-gray-200 transition-colors"
                >
                  {term}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Product Categories Grid */}
      <ProductCategoriesGrid />

      {/* Community Call to Action */}
      <CommunityCallToAction />

      {/* Features Section */}
      <section className="py-16 bg-muted">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-8 text-center">
            Why Use <span className="halo-logo">Halo</span>?
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-xl font-bold mb-2">
                Real Opinions, Not Just Reviews
                </h3>
                <p className="text-muted-foreground">
                We analyze reviews to give you the honest truth, not just a star rating. Know what truly matters.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="text-xl font-bold mb-2">No BS. Ever</h3>
                <p className="text-muted-foreground">
                Every product listed is legitimate and meets our quality standard. Say goodbye to scams, dropshipped and low-quality Bs.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <h3 className="text-xl font-bold mb-2">Shop Smart, Buy Confidently</h3>
                <p className="text-muted-foreground">
                Quick, clear insights help you choose better, faster. Your time is valuable, your purchases are safe.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Star className="h-5 w-5 text-primary" />
              <span className="text-2xl halo-logo">Halo</span>
            </div>

            <div className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} Halo. All rights
              reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
