import React, { useState } from "react";
import { Search, Menu, X, Star } from "lucide-react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import SimpleSearchAutocomplete from "./SimpleSearchAutocomplete";

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className = "" }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Don't show search bar on homepage
  const isHomePage = location.pathname === '/';

  return (
    <header className={`border-b bg-white sticky top-0 z-50 shadow-sm ${className}`}>
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        {/* Logo and mobile menu */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>

          <Link to="/" className="flex items-center space-x-2">
            <Star className="h-6 w-6 text-teal-600" />
            <h1 className="text-2xl md:text-3xl halo-logo">Halo</h1>
          </Link>
        </div>

        {/* Search bar - centered on desktop, hidden on mobile and homepage */}
        {!isHomePage && (
          <div className="hidden md:flex flex-1 max-w-xl mx-4">
            <SimpleSearchAutocomplete
              placeholder="Find real products opinions..."
              className="w-full"
              onSearch={(query) => {
                if (query.trim()) {
                  navigate(`/search?q=${encodeURIComponent(query)}`);
                }
              }}
            />
          </div>
        )}

        {/* Desktop navigation */}
        <nav className="hidden md:block">
          <ul className="flex space-x-6">
            <li>
              <Link to="/" className="text-sm font-medium hover:text-teal-600 transition-colors">
                Home
              </Link>
            </li>
            <li>
              <Link to="/categories" className="text-sm font-medium hover:text-teal-600 transition-colors">
                Categories
              </Link>
            </li>
            <li>
              <Link to="/trending" className="text-sm font-medium hover:text-teal-600 transition-colors">
                Trending
              </Link>
            </li>
            <li>
              <Link to="/about" className="text-sm font-medium hover:text-teal-600 transition-colors">
                About
              </Link>
            </li>
          </ul>
        </nav>

        {/* Mobile menu overlay */}
        {isMenuOpen && (
          <div className="fixed inset-0 bg-white z-40 pt-16 px-4 md:hidden">
            <div className="py-4">
              <nav>
                <ul className="space-y-4">
                  <li>
                    <Link
                      to="/"
                      className="text-lg font-medium block py-2 hover:text-teal-600 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Home
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/categories"
                      className="text-lg font-medium block py-2 hover:text-teal-600 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Categories
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/trending"
                      className="text-lg font-medium block py-2 hover:text-teal-600 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Trending
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/about"
                      className="text-lg font-medium block py-2 hover:text-teal-600 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      About
                    </Link>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        )}
      </div>

      {/* Mobile search bar - shown below header, hidden on homepage */}
      {!isHomePage && (
        <div className="md:hidden px-4 py-2 border-t">
          <SimpleSearchAutocomplete
            placeholder="Find honest product opinions..."
            className="w-full"
            onSearch={(query) => {
              if (query.trim()) {
                navigate(`/search?q=${encodeURIComponent(query)}`);
              }
            }}
          />
        </div>
      )}
    </header>
  );
};

export default Header;
