import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ThumbsUp, ThumbsDown, Minus } from 'lucide-react';

interface SentimentAspect {
  aspect: string;
  score: number;
  examples: string[];
}

interface SentimentAnalysis {
  summary: string;
  aspects: {
    positive: SentimentAspect[];
    negative: SentimentAspect[];
    neutral: SentimentAspect[];
  };
  averageSentiment: number;
}

interface ReviewSentimentAnalysisProps {
  sentimentAnalysis: SentimentAnalysis;
}

const ReviewSentimentAnalysis: React.FC<ReviewSentimentAnalysisProps> = ({
  sentimentAnalysis
}) => {
  if (!sentimentAnalysis) {
    return null;
  }

  const { summary, aspects } = sentimentAnalysis;

  // Format aspect name for display
  const formatAspectName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  // Get sentiment color based on score
  const getSentimentColor = (score: number) => {
    if (score > 0.2) return 'bg-green-100 text-green-800';
    if (score < -0.2) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  // Get sentiment icon based on score
  const getSentimentIcon = (score: number) => {
    if (score > 0.2) return <ThumbsUp className="h-4 w-4 mr-1" />;
    if (score < -0.2) return <ThumbsDown className="h-4 w-4 mr-1" />;
    return <Minus className="h-4 w-4 mr-1" />;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Opinion Analysis</CardTitle>
        <CardDescription>
          AI-powered analysis of customer opinions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Summary */}
          <div>
            <h3 className="text-sm font-medium mb-2">Summary</h3>
            <p className="text-sm text-muted-foreground">{summary}</p>
          </div>

          {/* Key Aspects */}
          <div>
            <h3 className="text-sm font-medium mb-2">Key Aspects</h3>

            {/* Positive Aspects */}
            {aspects.positive && aspects.positive.length > 0 && (
              <div className="mb-3">
                <h4 className="text-xs font-medium text-green-600 mb-1 flex items-center">
                  <ThumbsUp className="h-3 w-3 mr-1" /> Positive Highlights
                </h4>
                <div className="flex flex-wrap gap-2">
                  {aspects.positive.map((aspect, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className={getSentimentColor(aspect.score)}
                    >
                      {formatAspectName(aspect.aspect)}
                    </Badge>
                  ))}
                </div>
                {aspects.positive[0]?.examples && aspects.positive[0].examples.length > 0 && (
                  <div className="mt-1 text-xs text-muted-foreground italic">
                    "{aspects.positive[0].examples[0]}"
                  </div>
                )}
              </div>
            )}

            {/* Negative Aspects */}
            {aspects.negative && aspects.negative.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-red-600 mb-1 flex items-center">
                  <ThumbsDown className="h-3 w-3 mr-1" /> Areas for Improvement
                </h4>
                <div className="flex flex-wrap gap-2">
                  {aspects.negative.map((aspect, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className={getSentimentColor(aspect.score)}
                    >
                      {formatAspectName(aspect.aspect)}
                    </Badge>
                  ))}
                </div>
                {aspects.negative[0]?.examples && aspects.negative[0].examples.length > 0 && (
                  <div className="mt-1 text-xs text-muted-foreground italic">
                    "{aspects.negative[0].examples[0]}"
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewSentimentAnalysis;
