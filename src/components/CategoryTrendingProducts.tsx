import React, { useState, useEffect, useCallback } from "react";
import { useNavigate, Link } from "react-router-dom";
import { Card, CardContent } from "./ui/card";
import { Button } from "./ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Skeleton } from "./ui/skeleton";
import { Star, TrendingUp, ChevronRight } from "lucide-react";
import { ScrollArea, ScrollBar } from "./ui/scroll-area";
import { getOptimizedImageUrl } from "@/utils/imageUtils";
import { getTrendingByCategory, type Product } from "@/services/newApi";
import { safeNavigateToProduct } from "@/utils/productUtils";
import { trackProductClick } from "@/services/analytics";

const CategoryTrendingProducts = () => {
  const [categories, setCategories] = useState<Array<{ category: string; products: Product[] }>>([]);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState("all");
  const navigate = useNavigate();

  useEffect(() => {
    const loadTrendingProducts = async () => {
      try {
        setLoading(true);
        // Force refresh trending products to avoid stale data
        const response = await getTrendingByCategory(undefined, 'week', true);
        setCategories(response.data);
      } catch (error) {
        console.error("Error loading trending products:", error);
      } finally {
        setLoading(false);
      }
    };

    loadTrendingProducts();
  }, []);

  const handleProductClick = useCallback((productId: string, productSlug: string) => {
    // Track the product click
    trackProductClick(productId);

    // Navigate to the product page using the slug
    navigate(`/products/${productSlug}`);
  }, [navigate]);

  if (loading) {
    return <CategoryTrendingProductsSkeleton />;
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <TrendingUp className="h-6 w-6 text-primary mr-2" />
            <h2 className="text-2xl font-bold">Trending by Category</h2>
          </div>
        </div>

        <Tabs defaultValue="all" value={activeCategory} onValueChange={setActiveCategory}>
          <ScrollArea className="w-full whitespace-nowrap pb-2">
            <TabsList className="bg-transparent inline-flex">
              <TabsTrigger value="all">All Categories</TabsTrigger>
              {categories.map((category) => (
                <TabsTrigger key={category.category} value={category.category}>
                  {category.category}
                </TabsTrigger>
              ))}
            </TabsList>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>

          <TabsContent value="all" className="mt-6">
            {categories.map((category) => (
              <div key={category.category} className="mb-10">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold">{category.category}</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-primary flex items-center"
                    onClick={() => navigate(`/search?category=${encodeURIComponent(category.category)}`)}
                  >
                    View all <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {category.products.map((product) => (
                    <ProductCard
                      key={product._id}
                      product={product}
                      onClick={() => handleProductClick(product._id, product.slug)}
                    />
                  ))}
                </div>
              </div>
            ))}
          </TabsContent>

          {categories.map((category) => (
            <TabsContent key={category.category} value={category.category} className="mt-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {category.products.map((product) => (
                  <ProductCard
                    key={product._id}
                    product={product}
                    onClick={() => handleProductClick(product._id, product.slug)}
                  />
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
};

// Product Card Component
const ProductCard = ({ product, onClick }: { product: Product; onClick: () => void }) => {
  const handleClick = useCallback(() => {
    // Track the click
    trackProductClick(product._id);

    // Call the original onClick handler
    if (onClick) {
      onClick();
    }
  }, [product._id, onClick]);

  return (
    <Link to={`/products/${product.slug}`} className="block no-underline text-inherit">
      <Card className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow" onClick={handleClick}>
        <div className="aspect-square relative overflow-hidden">
          <img
            src={getOptimizedImageUrl(product.image, 400)}
            alt={product.name}
            className="object-cover w-full h-full transition-transform hover:scale-105"
          />
          {product.score >= 90 && (
            <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
              <Star className="h-3 w-3 mr-1 fill-white" /> Top Rated
            </div>
          )}
        </div>
        <CardContent className="p-4">
          <h3 className="font-medium text-sm line-clamp-2 h-10 mb-2">{product.name}</h3>
          <div className="flex items-center mb-2">
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
              <span className="text-sm font-medium">{(product.score / 20).toFixed(1)}</span>
            </div>
            <span className="text-xs text-muted-foreground ml-2">
              Score: {product.score}%
            </span>
          </div>
          <div className="flex items-center justify-between">
            <div className="text-xs bg-muted px-1.5 py-0.5 rounded-sm">
              {product.brand}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

// Skeleton loader for the component
const CategoryTrendingProductsSkeleton = () => {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="flex items-center mb-8">
          <Skeleton className="h-6 w-6 mr-2" />
          <Skeleton className="h-8 w-48" />
        </div>

        <div className="mb-6 border-b">
          <div className="flex gap-4 pb-2 overflow-x-auto">
            {[1, 2, 3, 4].map((i) => (
              <Skeleton key={i} className="h-10 w-28 flex-shrink-0" />
            ))}
          </div>
        </div>

        {[1, 2].map((categoryIndex) => (
          <div key={categoryIndex} className="mb-10">
            <div className="flex items-center justify-between mb-4">
              <Skeleton className="h-7 w-40" />
              <Skeleton className="h-8 w-24" />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((productIndex) => (
                <Card key={productIndex} className="overflow-hidden">
                  <Skeleton className="aspect-square w-full" />
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-3/4 mb-4" />
                    <Skeleton className="h-4 w-1/2 mb-2" />
                    <div className="flex gap-1">
                      <Skeleton className="h-4 w-12" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default CategoryTrendingProducts;
