import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSearchParams, useNavigate } from "react-router-dom";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import ProductCard from "./ProductCard";
import { getProducts } from "@/services/newApi";
import { Alert, AlertDescription } from "./ui/alert";
import { SearchResultsSkeleton } from "./ui/skeleton";
import { useErrorHandler } from "@/utils/errorHandler";
import { SlidersHorizontal, Search } from "lucide-react";
import Header from "./Header";

interface Product {
  _id: string;
  name: string;
  slug: string;
  category: string;
  brand: string;
  image: string;
  score: number;
}

const NewSearchResults = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Get values from URL parameters
  const searchQuery = searchParams.get("q") || "";
  const categoryParam = searchParams.get("category") || "";
  const initialSortOption = searchParams.get("sort") || "-createdAt";

  const [currentPage, setCurrentPage] = useState(parseInt(searchParams.get("page") || "1", 10));
  const [sortOption, setSortOption] = useState(initialSortOption);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const { handleError } = useErrorHandler();

  // Fetch products from API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Fetching products with params:', {
          q: searchQuery || undefined,
          category: categoryParam || undefined,
          sort: sortOption,
          page: currentPage,
          limit: 8
        });

        const response = await getProducts({
          category: categoryParam || undefined,
          sort: sortOption,
          page: currentPage,
          limit: 8 // Products per page
        });

        console.log('Received response:', response);

        setProducts(response.data);
        setTotalResults(response.total);
        setTotalPages(Math.ceil(response.total / 8));
      } catch (err) {
        const appError = handleError(err, 'Failed to load search results');
        setError(appError.message);
        // Use empty results
        setProducts([]);
        setTotalResults(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [searchQuery, categoryParam, sortOption, currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleSortChange = (value: string) => {
    setSortOption(value);
    // Update URL with new sort parameter
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("sort", value);
    navigate(`/search?${newSearchParams.toString()}`);
  };

  const handleProductClick = (product: Product) => {
    navigate(`/products/${product.slug}`);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center text-sm text-gray-600">
            <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
              Home
            </Button>
            <span className="mx-2">/</span>
            <span className="text-gray-900 font-medium">Search Results</span>
            {searchQuery && (
              <>
                <span className="mx-2">/</span>
                <span className="text-gray-900 font-medium truncate max-w-[200px]">"{searchQuery}"</span>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">


        {/* Search results header */}
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold mb-2 text-gray-900">
            {categoryParam && !searchQuery
              ? `${categoryParam} Products`
              : searchQuery
                ? `Results for "${searchQuery}"`
                : "All Products"
            }
            {categoryParam && searchQuery && ` in ${categoryParam}`}
          </h1>
          <p className="text-gray-600">
            Found {totalResults} products
            {categoryParam && !searchQuery && ` in ${categoryParam} category`}
          </p>
        </div>

        {/* Filters and sorting */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8 pb-4 border-b">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4" /> Filters
            </Button>

            {categoryParam && (
              <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200 flex items-center gap-1">
                {categoryParam}
                <button className="ml-1 text-gray-500 hover:text-gray-700" onClick={() => {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.delete("category");
                  navigate(`/search?${newSearchParams.toString()}`);
                }}>×</button>
              </Badge>
            )}
          </div>

          <div className="w-full sm:w-auto flex flex-row items-center gap-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            <Select value={sortOption} onValueChange={handleSortChange}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="-createdAt">Newest First</SelectItem>
                <SelectItem value="createdAt">Oldest First</SelectItem>
                <SelectItem value="name">Name (A-Z)</SelectItem>
                <SelectItem value="-name">Name (Z-A)</SelectItem>
                <SelectItem value="-score">Highest Score</SelectItem>
                <SelectItem value="score">Lowest Score</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Loading state */}
        {loading && <SearchResultsSkeleton />}

        {/* Error state */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Empty state */}
        {!loading && products.length === 0 && !error && (
          <div className="text-center py-12 bg-gray-50 rounded-lg border">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">No products found</p>
            <p className="text-gray-600 mb-6">Try adjusting your search or filters.</p>
            <Button onClick={() => navigate("/")}>Return to Home</Button>
          </div>
        )}

        {/* Product grid */}
        {!loading && products.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
            {products.map((product) => (
              <div key={product._id}>
                <ProductCard
                  id={product._id}
                  name={product.name}
                  image={product.image}
                  rating={4.5} // Default rating since we don't have this in our new schema
                  reviewCount={0} // Default review count
                  sources={[product.brand]}
                  score={product.score}
                  category={product.category}
                  onClick={() => handleProductClick(product)}
                />
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-8 mb-4">
            <Pagination>
              <PaginationContent className="flex flex-wrap justify-center">
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    className={
                      currentPage === 1 ? "pointer-events-none opacity-50" : ""
                    }
                  />
                </PaginationItem>

                {/* Mobile: Show limited page numbers */}
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                  // On mobile, only show current page, first, last, and one page before and after current
                  const showOnMobile =
                    page === 1 ||
                    page === totalPages ||
                    page === currentPage ||
                    page === currentPage - 1 ||
                    page === currentPage + 1;

                  // For desktop, show all pages
                  return (
                    <PaginationItem key={page} className={showOnMobile ? "" : "hidden sm:block"}>
                      {/* Add ellipsis for skipped pages on mobile */}
                      {showOnMobile && page > 1 && page === currentPage - 1 && currentPage > 3 && (
                        <PaginationEllipsis className="hidden xs:block" />
                      )}

                      <PaginationLink
                        isActive={currentPage === page}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </PaginationLink>

                      {/* Add ellipsis for skipped pages on mobile */}
                      {showOnMobile && page < totalPages && page === currentPage + 1 && currentPage < totalPages - 2 && (
                        <PaginationEllipsis className="hidden xs:block" />
                      )}
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() =>
                      handlePageChange(Math.min(totalPages, currentPage + 1))
                    }
                    className={
                      currentPage === totalPages
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>

            <div className="text-center text-sm text-gray-500 mt-2">
              Showing page {currentPage} of {totalPages}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewSearchResults;
