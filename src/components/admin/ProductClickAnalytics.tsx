import React, { useState, useEffect } from 'react';
import { getTrendingByClicks } from '@/services/analytics';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Loader2, BarChart2, TrendingUp, Calendar } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ClickAnalyticsProps {
  className?: string;
}

interface ProductClickData {
  product: {
    _id: string;
    name: string;
    slug: string;
    image: string;
    category: string;
    brand: string;
    score: number;
  };
  clickCount: number;
}

const ProductClickAnalytics: React.FC<ClickAnalyticsProps> = ({ className }) => {
  const [trendingProducts, setTrendingProducts] = useState<ProductClickData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeFrame, setTimeFrame] = useState<string>('7');
  const navigate = useNavigate();

  useEffect(() => {
    fetchTrendingProducts();
  }, [timeFrame]);

  const fetchTrendingProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await getTrendingByClicks(parseInt(timeFrame), 10);
      setTrendingProducts(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch trending products');
      console.error('Error fetching trending products by clicks:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleProductClick = (productId: string, productSlug: string) => {
    navigate(`/products/${productSlug}`);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl flex items-center">
            <BarChart2 className="mr-2 h-5 w-5" />
            Product Click Analytics
          </CardTitle>
          <Tabs value={timeFrame} onValueChange={setTimeFrame} className="w-auto">
            <TabsList>
              <TabsTrigger value="1">
                <Calendar className="h-4 w-4 mr-1" />
                24h
              </TabsTrigger>
              <TabsTrigger value="7">
                <Calendar className="h-4 w-4 mr-1" />
                7d
              </TabsTrigger>
              <TabsTrigger value="30">
                <Calendar className="h-4 w-4 mr-1" />
                30d
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <div className="text-center py-8 text-destructive">
            <p>{error}</p>
            <Button variant="outline" size="sm" className="mt-2" onClick={fetchTrendingProducts}>
              Retry
            </Button>
          </div>
        ) : trendingProducts.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No click data available for this time period.</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-sm font-medium text-muted-foreground mb-2">
              Top products by clicks in the past {timeFrame === '1' ? '24 hours' : timeFrame === '7' ? '7 days' : '30 days'}
            </div>
            {trendingProducts.map((item, index) => (
              <div
                key={item.product._id}
                className="flex items-center p-3 rounded-md hover:bg-muted cursor-pointer transition-colors"
                onClick={() => handleProductClick(item.product._id, item.product.slug)}
              >
                <div className="flex-shrink-0 w-10 h-10 bg-muted rounded-md flex items-center justify-center mr-3">
                  {item.product.image ? (
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-full h-full object-cover rounded-md"
                    />
                  ) : (
                    <div className="text-lg font-bold text-muted-foreground">{index + 1}</div>
                  )}
                </div>
                <div className="flex-grow min-w-0">
                  <div className="font-medium text-sm truncate">{item.product.name}</div>
                  <div className="text-xs text-muted-foreground flex items-center">
                    <span className="truncate">{item.product.category}</span>
                    <span className="mx-1">•</span>
                    <span>{item.product.brand}</span>
                  </div>
                </div>
                <div className="flex-shrink-0 flex items-center ml-2">
                  <div className="bg-primary/10 text-primary rounded-full px-2 py-1 text-xs font-medium flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    {item.clickCount} clicks
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProductClickAnalytics;
