import React, { useCallback } from "react";
import { Star, ExternalLink } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import { OptimizedImage } from "@/components/ui/optimized-image";
import { getOptimizedImageUrl } from "@/utils/imageUtils";
import { trackProductClick } from "@/services/analytics";

interface ProductCardProps {
  id: string;
  slug?: string;
  name: string;
  image: string;
  rating: number;
  reviewCount: number;
  sources: string[];
  score?: number;
  category?: string;
  onClick?: () => void;
}

const ProductCard = ({
  id = "1",
  slug,
  name = "Wireless Noise Cancelling Headphones",
  image = "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&q=80",
  rating = 4.5,
  reviewCount = 128,
  sources = ["Amazon", "Best Buy", "Walmart"],
  score,
  category,
  onClick,
}: ProductCardProps) => {
  // Generate stars based on rating
  const renderStars = () => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />,
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <Star className="h-4 w-4 text-yellow-400" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            </div>
          </div>,
        );
      } else {
        stars.push(<Star key={i} className="h-4 w-4 text-gray-300" />);
      }
    }
    return stars;
  };

  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 70) return 'bg-green-50 text-green-700 border-green-100';
    if (score >= 60) return 'bg-blue-50 text-blue-700 border-blue-100';
    if (score >= 50) return 'bg-yellow-50 text-yellow-700 border-yellow-100';
    if (score >= 40) return 'bg-orange-50 text-orange-700 border-orange-100';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  // Handle click with analytics tracking
  const handleClick = useCallback(() => {
    // Track the click
    trackProductClick(id);

    // Call the original onClick handler if provided
    if (onClick) {
      onClick();
    }
  }, [id, onClick]);

  return (
    <Link
      to={slug ? `/products/${slug}` : `/products/id/${id}`}
      className="block no-underline text-inherit w-full"
    >
      <Card
        className="w-full max-w-[280px] mx-auto h-[360px] overflow-hidden transition-all duration-200 hover:shadow-lg bg-white border border-gray-200 hover:border-gray-300 rounded-lg"
        onClick={handleClick}
      >
        <div className="h-40 overflow-hidden relative">
          <OptimizedImage
            src={getOptimizedImageUrl(image, 400)}
            alt={name}
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 280px"
            placeholderColor="#f5f5f5"
            fallbackSrc="/placeholder-image.jpg"
          />
          {category && (
            <Badge variant="outline" className="absolute top-2 left-2 bg-white/80 backdrop-blur-sm">
              {category}
            </Badge>
          )}
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-base sm:text-lg line-clamp-2 mb-2 text-gray-900">{name}</h3>

          <div className="flex items-center mb-3">
            <div className="flex mr-2">{renderStars()}</div>
            <span className="text-xs sm:text-sm text-gray-600">
              {rating.toFixed(1)} ({reviewCount})
            </span>
          </div>

          {score !== undefined && (
            <div className="mb-3">
              <Badge
                className={`text-sm px-2 py-1 font-medium ${getScoreColor(score)}`}
              >
                {score}% Score
              </Badge>
            </div>
          )}

          <div className="flex flex-wrap gap-1 mt-2">
            {sources.slice(0, 2).map((source, index) => (
              <Badge key={index} variant="outline" className="text-xs bg-gray-50">
                {source}
              </Badge>
            ))}
            {sources.length > 2 && (
              <Badge variant="outline" className="text-xs bg-gray-50">
                +{sources.length - 2}
              </Badge>
            )}
          </div>

          <div className="mt-3 pt-2 border-t border-gray-100 flex justify-between items-center">
            <span className="text-xs text-gray-500">Compare prices</span>
            <ExternalLink className="h-4 w-4 text-teal-600" />
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default ProductCard;
