import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { getProductById, clearProductCache } from "../services/newApi";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  ExternalLink,
  Loader2,
  ThumbsUp,
  ThumbsDown,
  // Star removed as requested
  Calendar,
  Share2
} from 'lucide-react';
import Header from '@/components/Header';
import { trackProductView } from "@/services/analytics";

const NewProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (!id) return;

    const fetchProduct = async () => {
      setLoading(true);
      setError(null);

      try {
        // Try to fetch with force refresh to avoid stale cache
        const data = await getProductById(id, true);
        setProduct(data);

        // Track product view for analytics
        trackProductView(id);
      } catch (err) {
        // If there's an error, clear the product cache to prevent future issues
        clearProductCache(id);
        setError(err instanceof Error ? err.message : 'Failed to fetch product details');
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800 border-green-200';
    if (score >= 70) return 'bg-green-50 text-green-700 border-green-100';
    if (score >= 60) return 'bg-blue-50 text-blue-700 border-blue-100';
    if (score >= 50) return 'bg-yellow-50 text-yellow-700 border-yellow-100';
    if (score >= 40) return 'bg-orange-50 text-orange-700 border-orange-100';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  const getScoreText = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 70) return 'Very Good';
    if (score >= 60) return 'Good';
    if (score >= 50) return 'Average';
    if (score >= 40) return 'Below Average';
    return 'Poor';
  };

  // Star ratings removed as requested

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-12 flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-teal-600 mb-4" />
          <p className="text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error || 'Product not found'}</AlertDescription>
          </Alert>
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Products
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Breadcrumb navigation */}
      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center text-sm text-gray-600">
            <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
              Home
            </Button>
            <span className="mx-2">/</span>
            <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/products')}>
              Products
            </Button>
            <span className="mx-2">/</span>
            <Button variant="link" className="p-0 h-auto" onClick={() => navigate(`/categories/${product.category}`)}>
              {product.category}
            </Button>
            <span className="mx-2">/</span>
            <span className="text-gray-900 font-medium truncate max-w-[200px]">{product.name}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Product image */}
          <div>
            <div className="rounded-lg overflow-hidden border bg-white p-4 flex items-center justify-center h-[400px]">
              <img
                src={product.image}
                alt={product.name}
                className="max-w-full max-h-full object-contain"
              />
            </div>

            {/* Image gallery thumbnails would go here */}
            <div className="flex mt-4 gap-2 overflow-x-auto pb-2">
              {[1, 2, 3].map((i) => (
                <div key={i} className="w-20 h-20 border rounded flex-shrink-0 bg-white p-1">
                  <img
                    src={product.image}
                    alt={`${product.name} thumbnail ${i}`}
                    className="w-full h-full object-contain"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Product details */}
          <div>
            <div className="flex flex-wrap items-start gap-2 mb-2">
              <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{product.category}</Badge>
              <Badge variant="outline">{product.brand}</Badge>
            </div>

            <h1 className="text-2xl md:text-3xl font-bold mb-3 text-gray-900">{product.name}</h1>

            {/* Opinion count removed as requested */}

            <div className="mb-4">
              <Badge
                className={`text-sm px-3 py-1 ${getScoreColor(product.score)}`}
              >
                {product.score}% Score
              </Badge>
              <span className="ml-2 text-sm text-gray-700">
                {getScoreText(product.score)}
              </span>
            </div>

            <p className="text-gray-700 mb-6 line-clamp-4">{product.description}</p>

            {product.affiliateLink && (
              <Button className="w-full mb-6 bg-teal-600 hover:bg-teal-700 text-white" asChild>
                <a href={product.affiliateLink} target="_blank" rel="noopener noreferrer">
                  Check it out <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </Button>
            )}

            {/* Available from section removed as requested */}

            {/* Pros and Cons */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
              <Card className="border-green-100">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center text-green-700">
                    <ThumbsUp className="mr-2 h-4 w-4 text-green-600" /> Pros
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {product.pros && product.pros.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-1">
                      {product.pros.map((pro: string, index: number) => (
                        <li key={index} className="text-sm text-gray-700">{pro}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No pros listed</p>
                  )}
                </CardContent>
              </Card>

              <Card className="border-red-100">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center text-red-700">
                    <ThumbsDown className="mr-2 h-4 w-4 text-red-600" /> Cons
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {product.cons && product.cons.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-1">
                      {product.cons.map((con: string, index: number) => (
                        <li key={index} className="text-sm text-gray-700">{con}</li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No cons listed</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Share buttons */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" /> Share
              </Button>
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" /> Price History
              </Button>
            </div>
          </div>
        </div>

        {/* Simplified tabs - only Opinions as requested */}
        <Tabs defaultValue="opinions" className="mt-8" onValueChange={setActiveTab}>
          <TabsList className="w-full border-b rounded-none justify-start">
            <TabsTrigger value="opinions" className="flex-1 sm:flex-none">Opinions</TabsTrigger>
          </TabsList>

          {/* Overview tab removed as requested */}

          <TabsContent value="opinions" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>User Opinions</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Opinion filters */}
                <div className="flex flex-wrap gap-2 mb-6">
                  <Badge variant="outline" className="bg-gray-50 cursor-pointer">All Sources</Badge>
                  <Badge variant="outline" className="bg-gray-50 cursor-pointer">Amazon</Badge>
                  <Badge variant="outline" className="bg-gray-50 cursor-pointer">Best Buy</Badge>
                  <Badge variant="outline" className="bg-gray-50 cursor-pointer">Walmart</Badge>
                </div>

                {/* Backend opinions display */}
                <div className="space-y-4">
                  <div className="text-center py-8 text-gray-600">
                    <p className="text-lg font-medium mb-2">Professional Analysis</p>
                    <p className="text-sm">
                      Our expert opinions are based on comprehensive product analysis and market research.
                    </p>
                  </div>

                  {product.opinion && (
                    <Card className="border-gray-200">
                      <CardContent className="p-6">
                        <div className="flex items-start mb-4">
                          <div className="w-10 h-10 rounded-full bg-teal-100 flex items-center justify-center mr-3">
                            <span className="text-teal-600 font-semibold text-sm">H</span>
                          </div>
                          <div>
                            <p className="font-medium text-sm text-gray-900">Halo Expert Analysis</p>
                            <p className="text-xs text-gray-500">Professional Review</p>
                          </div>
                        </div>
                        <p className="text-gray-700 leading-relaxed">
                          {product.opinion}
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Load more button removed - showing backend opinion only */}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Specifications tab removed as requested */}
        </Tabs>
      </div>
    </div>
  );
};

export default NewProductDetailPage;
