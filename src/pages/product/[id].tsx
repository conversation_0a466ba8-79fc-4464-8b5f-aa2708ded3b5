import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  Star,
  ThumbsUp,
  ThumbsDown,
  Filter,
  Calendar,
  TrendingUp,
  DollarSign,
  Loader2,
} from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import ReviewList from "@/components/ReviewList";
import ReviewListWithAPI from "@/components/ReviewListWithAPI";
import PriceComparison from "@/components/PriceComparison";
import PriceComparisonWithAPI from "@/components/PriceComparisonWithAPI";
import { getProductById, type Product } from "@/services/api";

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);
        const data = await getProductById(id);
        setProduct(data);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError('Failed to load product details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  // Star ratings removed as requested

  // Calculate average rating
  const calculateAverageRating = () => {
    if (!product || !product.platforms || product.platforms.length === 0) return 0;

    const totalRating = product.platforms.reduce((sum, platform) => sum + (platform.rating || 0), 0);
    return product.platforms.length > 0 ? totalRating / product.platforms.length : 0;
  };

  // Calculate total review count
  const calculateTotalReviews = () => {
    if (!product || !product.platforms) return 0;

    return product.platforms.reduce((sum, platform) => sum + (platform.reviewCount || 0), 0);
  };

  // Generate rating breakdown
  const generateRatingBreakdown = () => {
    if (!product || !product.reviews || product.reviews.length === 0) {
      return [
        { stars: 5, percentage: 0 },
        { stars: 4, percentage: 0 },
        { stars: 3, percentage: 0 },
        { stars: 2, percentage: 0 },
        { stars: 1, percentage: 0 },
      ];
    }

    const ratings = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };

    product.reviews.forEach(review => {
      const rating = Math.round(review.rating);
      if (rating >= 1 && rating <= 5) {
        ratings[rating as keyof typeof ratings]++;
      }
    });

    const total = product.reviews.length;

    return [
      { stars: 5, percentage: Math.round((ratings[5] / total) * 100) || 0 },
      { stars: 4, percentage: Math.round((ratings[4] / total) * 100) || 0 },
      { stars: 3, percentage: Math.round((ratings[3] / total) * 100) || 0 },
      { stars: 2, percentage: Math.round((ratings[2] / total) * 100) || 0 },
      { stars: 1, percentage: Math.round((ratings[1] / total) * 100) || 0 },
    ];
  };

  // Loading state
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading product details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-4">
          <Button variant="link" onClick={() => navigate("/")} className="p-0">
            ← Back to Home
          </Button>
        </div>
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error || 'Product not found'}</AlertDescription>
        </Alert>
        <Button onClick={() => window.location.reload()}>Try Again</Button>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8 bg-background">
        <div className="mb-4">
          <Button variant="link" onClick={() => navigate(-1)} className="p-0">
            ← Back to Search Results
          </Button>
        </div>
        {loading ? (
          <div className="flex items-center justify-center h-96">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <span className="ml-2 text-xl">Loading product details...</span>
          </div>
        ) : (
          <Alert variant="destructive" className="max-w-2xl mx-auto">
            <AlertDescription className="text-center py-4">
              {error || 'Failed to load product details. Please try again.'}
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  }

  // Calculate values
  const avgRating = calculateAverageRating();
  const totalReviews = calculateTotalReviews();
  const ratingBreakdown = generateRatingBreakdown();
  const sources = product.platforms ? product.platforms.map(platform => platform.name) : [];

  // Generate review highlights
  const highlights = [
    { type: "positive", text: "Good product quality and performance" },
    { type: "positive", text: "Fast shipping and delivery" },
    { type: "negative", text: "Some users reported issues with durability" },
  ];

  if (product.reviews && product.reviews.length > 0) {
    // Add real highlights from reviews if available
    highlights.length = 0;

    // Find positive reviews
    const positiveReviews = product.reviews.filter(review => review.rating >= 4);
    if (positiveReviews.length > 0) {
      for (let i = 0; i < Math.min(3, positiveReviews.length); i++) {
        const review = positiveReviews[i];
        highlights.push({
          type: "positive",
          text: review.title || review.body.substring(0, 50) + (review.body.length > 50 ? '...' : '')
        });
      }
    }

    // Find negative reviews
    const negativeReviews = product.reviews.filter(review => review.rating <= 2);
    if (negativeReviews.length > 0) {
      for (let i = 0; i < Math.min(2, negativeReviews.length); i++) {
        const review = negativeReviews[i];
        highlights.push({
          type: "negative",
          text: review.title || review.body.substring(0, 50) + (review.body.length > 50 ? '...' : '')
        });
      }
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 bg-background">
      {/* Back button */}
      <div className="mb-4">
        <Button variant="link" onClick={() => navigate(-1)} className="p-0">
          ← Back to Search Results
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Product Images */}
        <div className="md:col-span-1">
          <div className="rounded-lg overflow-hidden mb-4">
            <img
              src={product.imageUrls && product.imageUrls.length > 0
                ? product.imageUrls[0]
                : "https://via.placeholder.com/500"}
              alt={product.name}
              className="w-full h-auto object-cover"
            />
          </div>
          <div className="grid grid-cols-2 gap-2">
            {product.imageUrls && product.imageUrls.slice(1).map((image, index) => (
              <div key={index} className="rounded-lg overflow-hidden">
                <img
                  src={image}
                  alt={`${product.name} - view ${index + 2}`}
                  className="w-full h-auto object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="md:col-span-2">
          <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
          {/* Opinion count removed as requested */}

          <p className="text-muted-foreground mb-6">{product.description}</p>

          {/* Sources section removed as requested */}

          <Tabs
            defaultValue="reviews"
            className="w-full"
            onValueChange={setActiveTab}
          >
            <TabsList className="grid w-full grid-cols-1">
              <TabsTrigger value="reviews">Opinions</TabsTrigger>
            </TabsList>

            {/* Other tabs removed as requested - only showing opinions */}

            <TabsContent value="reviews" className="pt-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
                <h2 className="text-2xl font-bold">
                  All Opinions ({totalReviews})
                </h2>
                <div className="flex flex-wrap gap-2">
                  <Select defaultValue="recent">
                    <SelectTrigger className="w-[180px]">
                      <Calendar className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recent">Most Recent</SelectItem>
                      <SelectItem value="oldest">Oldest First</SelectItem>
                      <SelectItem value="highest">Highest Rated</SelectItem>
                      <SelectItem value="lowest">Lowest Rated</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Opinions</SelectItem>
                      <SelectItem value="positive">Positive</SelectItem>
                      <SelectItem value="neutral">Neutral</SelectItem>
                      <SelectItem value="negative">Negative</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px]">
                      <TrendingUp className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sources</SelectItem>
                      {sources.map((source) => (
                        <SelectItem key={source} value={source.toLowerCase()}>
                          {source}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <ReviewListWithAPI productId={product.id} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
