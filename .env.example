# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=5005

# Frontend URL for CORS (in development)
CORS_ORIGIN=http://localhost:5173

# MongoDB Connection
MONGODB_URI=mongodb+srv://username:<EMAIL>/halo-review?retryWrites=true&w=majority

# JWT Authentication
JWT_SECRET=your_jwt_secret_key_change_in_production
JWT_EXPIRES_IN=30d

# Admin User (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=securepassword

# Optional: Vite Configuration
VITE_BASE_PATH=/
