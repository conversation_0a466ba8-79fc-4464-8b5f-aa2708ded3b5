const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const dotenv = require('dotenv');
const mongoose = require('mongoose');
// Rate limiting removed as requested
const swaggerUi = require('swagger-ui-express');
const swaggerDocs = require('./config/swagger');
const connectDB = require('./config/db');
const errorHandler = require('./middleware/errorHandler');
const { applySecurityMiddleware } = require('./middleware/security');
const { scheduleTrendingUpdates } = require('./utils/trendingUpdater');

// Load environment variables
dotenv.config({ path: './config.env' });

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Connect to MongoDB - this is async but we don't await it here
// We'll handle database operations after connection is established
connectDB();

// Middleware
app.use(express.json());

// In production, we don't need CORS as frontend and backend are served from the same origin
if (process.env.NODE_ENV === 'development') {
  app.use(cors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
    credentials: true // Allow cookies to be sent with requests
  }));
}

// Logging
app.use(morgan('dev'));

// Apply security middleware (Helmet, CSRF, XSS protection, etc.)
applySecurityMiddleware(app);

// Rate limiting removed as requested

// API Routes
app.use('/api/auth', require('./routes/auth.routes'));
app.use('/api/products', require('./routes/product.routes'));
app.use('/api/admin', require('./routes/admin.routes'));
app.use('/api/categories', require('./routes/category.routes'));
app.use('/api/search', require('./routes/search.routes'));
app.use('/api/analytics', require('./routes/analytics.routes'));

// Health check endpoint for Render
app.get('/api/health', (_, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// Swagger API Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// Serve static files from the React app in production
if (process.env.NODE_ENV === 'production') {
  // Set static folder
  app.use(express.static(path.join(__dirname, '../dist')));

  // Handle any requests that don't match the ones above
  app.get('*', (req, res) => {
    // Don't serve the React app for API routes
    if (req.path.startsWith('/api') || req.path.startsWith('/api-docs')) {
      return res.status(404).json({ message: 'API endpoint not found' });
    }
    res.sendFile(path.join(__dirname, '../dist/index.html'));
  });
} else {
  // Base route for development
  app.get('/', (_, res) => {
    res.json({ message: 'Welcome to Halo Review API' });
  });
}

// Error handling middleware
app.use(errorHandler);

// Start server
const server = app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);

  // Wait for MongoDB connection to be established before scheduling trending updates
  const dbConnectionCheck = setInterval(() => {
    if (mongoose.connection.readyState === 1) {
      console.log('MongoDB connection established. Starting trending products scheduler...');
      clearInterval(dbConnectionCheck);

      // Schedule trending products updates
      scheduleTrendingUpdates();
    } else {
      console.log(`Waiting for MongoDB connection... (state: ${mongoose.connection.readyState})`);
    }
  }, 5000); // Check every 5 seconds

  // Stop checking after 2 minutes to prevent infinite loop
  setTimeout(() => {
    if (mongoose.connection.readyState !== 1) {
      console.warn('MongoDB connection not established after 2 minutes. Stopping connection check.');
      clearInterval(dbConnectionCheck);
    }
  }, 120000);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.log('UNHANDLED REJECTION! 💥 Shutting down...');
  console.log(err.name, err.message);

  // Graceful shutdown
  if (server) {
    server.close(() => {
      console.log('Server closed');
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.log('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  console.log(err.name, err.message);
  process.exit(1);
});
