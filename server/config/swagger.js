const swaggerJsDoc = require('swagger-jsdoc');

const swaggerOptions = {
  swaggerDefinition: {
    openapi: '3.0.0',
    info: {
      title: 'Halo Review API',
      version: '1.0.0',
      description: 'API for Halo Review product comparison app - No web scraping functionality',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: '/api',
        description: 'API server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      }
    }
  },
  apis: ['./server/routes/*.js']
};

const swaggerDocs = swaggerJsDoc(swaggerOptions);

module.exports = swaggerDocs;
