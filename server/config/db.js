const mongoose = require('mongoose');

// MongoDB connection options
const mongooseOptions = {
  serverSelectionTimeoutMS: 30000, // Timeout for server selection (30 seconds)
  socketTimeoutMS: 45000, // Socket timeout (45 seconds)
  connectTimeoutMS: 30000, // Connection timeout (30 seconds)
  maxPoolSize: 10, // Maximum number of connections in the pool
  minPoolSize: 2, // Minimum number of connections in the pool
  retryWrites: true, // <PERSON><PERSON> writes if they fail
  retryReads: true, // Retry reads if they fail
};

// Connection state tracking
let isConnected = false;

const connectDB = async () => {
  // If already connected, return
  if (isConnected) {
    console.log('MongoDB is already connected');
    return;
  }

  // Check if MONGODB_URI is defined
  if (!process.env.MONGODB_URI) {
    console.error('MONGODB_URI is not defined in environment variables');
    process.exit(1);
  }

  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, mongooseOptions);
    isConnected = true;
    console.log(`MongoDB Connected: ${conn.connection.host}`);

    // Set up connection event handlers
    mongoose.connection.on('error', (err) => {
      console.error(`MongoDB connection error: ${err}`);
      isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('MongoDB disconnected. Attempting to reconnect...');
      isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      console.log('MongoDB reconnected');
      isConnected = true;
    });

  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error.message}`);
    isConnected = false;

    // Don't exit the process, allow for retry
    if (process.env.NODE_ENV === 'production') {
      console.log('Retrying connection in 5 seconds...');
      setTimeout(connectDB, 5000);
    } else {
      process.exit(1);
    }
  }
};

// Export connection state and function
module.exports = connectDB;
module.exports.isConnected = () => isConnected;
