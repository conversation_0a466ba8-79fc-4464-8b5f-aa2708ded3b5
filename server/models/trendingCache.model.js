const mongoose = require('mongoose');

/**
 * Trending Cache Schema
 * Used to store pre-calculated trending products by category
 */
const trendingCacheSchema = new mongoose.Schema({
  type: { 
    type: String, 
    required: true 
  },
  timeFrame: { 
    type: String, 
    required: true,
    enum: ['day', 'week', 'month']
  },
  data: { 
    type: mongoose.Schema.Types.Mixed, 
    required: true 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Create a compound index for efficient lookups
trendingCacheSchema.index({ type: 1, timeFrame: 1 });

module.exports = mongoose.model('TrendingCache', trendingCacheSchema);
