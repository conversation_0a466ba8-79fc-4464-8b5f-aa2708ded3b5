const Product = require('../models/Product');

/**
 * @desc    Search products by query
 * @route   GET /api/search
 * @access  Public
 */
exports.searchProducts = async (req, res) => {
  try {
    const { q, category, brand, minScore, maxScore, sort = 'textScore', page = 1, limit = 10 } = req.query;

    // Build query object
    const query = {};

    // Add text search if query is provided
    if (q && q.trim() !== '') {
      query.$text = { $search: q };
    }

    // Add filters if provided
    if (category) {
      query.category = category;
    }

    if (brand) {
      query.brand = brand;
    }

    // Add score range if provided
    if (minScore || maxScore) {
      query.score = {};
      if (minScore) query.score.$gte = parseInt(minScore);
      if (maxScore) query.score.$lte = parseInt(maxScore);
    }

    // If no search criteria provided, return empty array
    if (Object.keys(query).length === 0) {
      return res.status(200).json({
        success: true,
        data: []
      });
    }

    // Calculate pagination
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    // Determine sort order
    let sortOptions = {};
    if (q && q.trim() !== '' && sort === 'textScore') {
      // Use text score for sorting when doing a text search
      sortOptions = { score: { $meta: 'textScore' } };
    } else if (sort === 'score') {
      sortOptions = { score: -1 };
    } else if (sort === 'newest') {
      sortOptions = { createdAt: -1 };
    } else if (sort === 'updated') {
      sortOptions = { updatedAt: -1 };
    } else if (sort === 'name') {
      sortOptions = { name: 1 };
    } else {
      // Default sort by text score if doing text search, otherwise by score
      sortOptions = q && q.trim() !== '' ? { score: { $meta: 'textScore' } } : { score: -1 };
    }

    // Execute query with pagination and sorting
    const products = await Product.find(query)
      .select('name slug brand category image score')
      .skip(skip)
      .limit(limitNum)
      .sort(sortOptions);

    // Get total count for pagination
    const total = await Product.countDocuments(query);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    // If no results from text search and we were doing a text search, try regex as fallback
    if (products.length === 0 && q && q.trim() !== '') {
      // Create regex query for fallback
      const regexQuery = {
        $or: [
          { name: { $regex: q, $options: 'i' } },
          { brand: { $regex: q, $options: 'i' } },
          { category: { $regex: q, $options: 'i' } },
          { description: { $regex: q, $options: 'i' } }
        ]
      };

      // Add the same filters as before
      if (category) regexQuery.category = category;
      if (brand) regexQuery.brand = brand;
      if (query.score) regexQuery.score = query.score;

      // Execute regex query
      const regexProducts = await Product.find(regexQuery)
        .select('name slug brand category image score')
        .skip(skip)
        .limit(limitNum)
        .sort({ name: 1 });

      // Get total count for regex query
      const regexTotal = await Product.countDocuments(regexQuery);
      const regexTotalPages = Math.ceil(regexTotal / limitNum);

      return res.status(200).json({
        success: true,
        count: regexProducts.length,
        pagination: {
          page: pageNum,
          limit: limitNum,
          totalPages: regexTotalPages,
          total: regexTotal,
          hasNextPage: pageNum < regexTotalPages,
          hasPrevPage: pageNum > 1
        },
        data: regexProducts
      });
    }

    res.status(200).json({
      success: true,
      count: products.length,
      pagination: {
        page: pageNum,
        limit: limitNum,
        totalPages,
        total,
        hasNextPage,
        hasPrevPage
      },
      data: products
    });
  } catch (err) {
    console.error('Search error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get search suggestions
 * @route   GET /api/search/suggestions
 * @access  Public
 */
exports.getSearchSuggestions = async (req, res) => {
  try {
    const { q } = req.query;

    if (!q || q.trim() === '') {
      return res.status(200).json({
        success: true,
        data: []
      });
    }

    // For product name suggestions, use prefix matching for better autocomplete
    const nameRegex = new RegExp(`^${q}`, 'i');

    // For brand and category, we can use the text index if the query is long enough
    // Otherwise use regex for shorter queries (better for autocomplete)
    const useTextIndex = q.length >= 3;

    // Get distinct product names, brands, and categories that match the query
    const [products, brands, categories] = await Promise.all([
      // For product names, always use regex with prefix matching for better autocomplete
      Product.find({ name: { $regex: nameRegex } })
        .select('name slug')
        .limit(5)
        .sort({ name: 1 })
        .hint({ name: 1 }), // Use name index if available

      // For brands, use text index for longer queries, regex for shorter ones
      useTextIndex
        ? Product.distinct('brand', { $text: { $search: q } })
        : Product.distinct('brand', { brand: { $regex: new RegExp(q, 'i') } }),

      // For categories, use text index for longer queries, regex for shorter ones
      useTextIndex
        ? Product.distinct('category', { $text: { $search: q } })
        : Product.distinct('category', { category: { $regex: new RegExp(q, 'i') } })
    ]);

    // Format suggestions
    const productSuggestions = products.map(p => ({
      type: 'product',
      text: p.name,
      slug: p.slug
    }));

    const brandSuggestions = brands.slice(0, 5).map(brand => ({
      type: 'brand',
      text: brand
    }));

    const categorySuggestions = categories.slice(0, 5).map(category => ({
      type: 'category',
      text: category
    }));

    // Combine all suggestions
    const suggestions = [
      ...productSuggestions,
      ...brandSuggestions,
      ...categorySuggestions
    ].slice(0, 10);

    res.status(200).json({
      success: true,
      data: suggestions
    });
  } catch (err) {
    console.error('Search suggestions error:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
