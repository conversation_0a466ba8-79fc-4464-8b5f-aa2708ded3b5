const ProductClick = require('../models/ProductClick');
const Product = require('../models/Product');
const { validationResult } = require('express-validator');

/**
 * @desc    Track a product click
 * @route   POST /api/analytics/product-click
 * @access  Public
 */
exports.trackProductClick = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { productId } = req.body;

    // Verify the product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Create click record
    const clickData = {
      productId,
      // If user is authenticated, store userId
      userId: req.user ? req.user.id : null,
      // Store session ID if available
      sessionId: req.body.sessionId || null,
      // Store IP address (with privacy considerations)
      ipAddress: req.ip || req.connection.remoteAddress,
      // Store user agent
      userAgent: req.headers['user-agent'] || null,
      // Store referrer if available
      referrer: req.headers.referer || req.body.referrer || null
    };

    await ProductClick.create(clickData);

    res.status(200).json({
      success: true,
      message: 'Click tracked successfully'
    });
  } catch (err) {
    console.error('Error tracking product click:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get click count for a product
 * @route   GET /api/analytics/product-clicks/:id
 * @access  Private (Admin)
 */
exports.getProductClickCount = async (req, res) => {
  try {
    const productId = req.params.id;

    // Verify the product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Get click count
    const count = await ProductClick.countDocuments({ productId });

    res.status(200).json({
      success: true,
      data: {
        productId,
        productName: product.name,
        clickCount: count
      }
    });
  } catch (err) {
    console.error('Error getting product click count:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get trending products based on clicks
 * @route   GET /api/analytics/trending-clicks
 * @access  Private (Admin)
 */
exports.getTrendingByClicks = async (req, res) => {
  try {
    const { days = 7, limit = 10 } = req.query;

    // Get trending products
    const trending = await ProductClick.getTrendingProducts({
      days: parseInt(days),
      limit: parseInt(limit)
    });

    // Get product details for the trending products
    const productIds = trending.map(item => item._id);
    const products = await Product.find({ _id: { $in: productIds } });

    // Combine click data with product details
    const result = trending.map(item => {
      const product = products.find(p => p._id.toString() === item._id.toString());
      return {
        product: product || { _id: item._id, name: 'Unknown Product' },
        clickCount: item.count
      };
    });

    res.status(200).json({
      success: true,
      data: result
    });
  } catch (err) {
    console.error('Error getting trending products by clicks:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
