const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Product = require('../models/Product');
const User = require('../models/User');

// Load environment variables
dotenv.config({ path: './config.env' });

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('MongoDB Connected'))
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

/**
 * Create indexes for the Product collection
 */
const createProductIndexes = async () => {
  try {
    console.log('Creating indexes for Product collection...');

    // Get existing indexes
    const indexInfo = await Product.collection.indexInformation();
    console.log('Current indexes:', Object.keys(indexInfo));

    // Drop existing text index if it exists
    try {
      if (indexInfo['name_text_description_text_brand_text_category_text']) {
        await Product.collection.dropIndex('name_text_description_text_brand_text_category_text');
        console.log('Dropped existing text index');
      }
    } catch (err) {
      console.log('No existing text index to drop or error dropping index:', err.message);
    }

    // Create optimized text index with weights
    try {
      await Product.collection.createIndex(
        {
          name: 'text',
          brand: 'text',
          category: 'text',
          description: 'text'
        },
        {
          weights: {
            name: 10,      // Name is most important
            brand: 5,       // Brand is second most important
            category: 3,    // Category is third most important
            description: 1  // Description is least important
          },
          name: 'product_text_index',
          background: true
        }
      );
      console.log('Created text index with weights');
    } catch (err) {
      console.log('Text index already exists or error creating index:', err.message);
    }

    // Create compound index for category and brand (common filters)
    try {
      await Product.collection.createIndex(
        { category: 1, brand: 1 },
        { name: 'category_brand_index', background: true }
      );
      console.log('Created category_brand_index');
    } catch (err) {
      console.log('Category-brand index already exists or error creating index:', err.message);
    }

    // Create index for score (used for sorting)
    try {
      await Product.collection.createIndex(
        { score: -1 },
        { name: 'score_index', background: true }
      );
      console.log('Created score_index');
    } catch (err) {
      console.log('Score index already exists or error creating index:', err.message);
    }

    // Create compound index for category and score (for filtered sorting)
    try {
      await Product.collection.createIndex(
        { category: 1, score: -1 },
        { name: 'category_score_index', background: true }
      );
      console.log('Created category_score_index');
    } catch (err) {
      console.log('Category-score index already exists or error creating index:', err.message);
    }

    // Create index for brand (used for filtering)
    try {
      await Product.collection.createIndex(
        { brand: 1 },
        { name: 'brand_index', background: true }
      );
      console.log('Created brand_index');
    } catch (err) {
      console.log('Brand index already exists or error creating index:', err.message);
    }

    // Create index for name (used for autocomplete)
    try {
      await Product.collection.createIndex(
        { name: 1 },
        { name: 'name_index', background: true }
      );
      console.log('Created name_index');
    } catch (err) {
      console.log('Name index already exists or error creating index:', err.message);
    }

    // Create index for createdAt (used for sorting by newest)
    try {
      await Product.collection.createIndex(
        { createdAt: -1 },
        { name: 'createdAt_index', background: true }
      );
      console.log('Created createdAt_index');
    } catch (err) {
      console.log('CreatedAt index already exists or error creating index:', err.message);
    }

    // Create index for updatedAt (used for sorting by recently updated)
    try {
      await Product.collection.createIndex(
        { updatedAt: -1 },
        { name: 'updatedAt_index', background: true }
      );
      console.log('Created updatedAt_index');
    } catch (err) {
      console.log('UpdatedAt index already exists or error creating index:', err.message);
    }

    console.log('All Product indexes created successfully');
  } catch (err) {
    console.error('Error creating Product indexes:', err);
  }
};

/**
 * Create indexes for the User collection
 */
const createUserIndexes = async () => {
  try {
    console.log('Creating indexes for User collection...');

    // Get existing indexes
    const indexInfo = await User.collection.indexInformation();
    console.log('Current User indexes:', Object.keys(indexInfo));

    // Email index is already created by the schema definition with unique: true
    console.log('Email index is already created by the schema definition');

    // Create index for role (used for filtering admin users)
    try {
      await User.collection.createIndex(
        { role: 1 },
        { name: 'role_index', background: true }
      );
      console.log('Created role_index');
    } catch (err) {
      console.log('Role index already exists or error creating index:', err.message);
    }

    // Create index for createdAt (used for sorting)
    try {
      await User.collection.createIndex(
        { createdAt: -1 },
        { name: 'user_createdAt_index', background: true }
      );
      console.log('Created user_createdAt_index');
    } catch (err) {
      console.log('User createdAt index already exists or error creating index:', err.message);
    }

    console.log('All User indexes created successfully');
  } catch (err) {
    console.error('Error creating User indexes:', err);
  }
};

/**
 * Main function to create all indexes
 */
const createAllIndexes = async () => {
  try {
    await createProductIndexes();
    await createUserIndexes();

    console.log('All indexes created successfully');
    process.exit(0);
  } catch (err) {
    console.error('Error creating indexes:', err);
    process.exit(1);
  }
};

// Run the function
createAllIndexes();
