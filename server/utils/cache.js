const NodeCache = require('node-cache');

// Create a new cache instance with default TTL of 10 minutes
const cache = new NodeCache({
  stdTTL: 600, // 10 minutes in seconds
  checkperiod: 120, // Check for expired keys every 2 minutes
  useClones: false // Don't clone objects when getting/setting (for performance)
});

/**
 * Cache middleware for Express routes
 * @param {number} duration - Cache duration in seconds (overrides default TTL)
 * @returns {Function} Express middleware function
 */
const cacheMiddleware = (duration) => {
  return (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Create a cache key from the request URL and query parameters
    const key = req.originalUrl || req.url;
    
    // Try to get cached response
    const cachedResponse = cache.get(key);
    
    if (cachedResponse) {
      // Return cached response
      console.log(`Cache hit for ${key}`);
      return res.json(cachedResponse);
    }

    // If not cached, intercept res.json to cache the response
    const originalJson = res.json;
    res.json = function(body) {
      // Cache the response
      if (body && typeof body === 'object') {
        console.log(`Caching response for ${key}`);
        cache.set(key, body, duration);
      }
      
      // Call the original res.json method
      return originalJson.call(this, body);
    };
    
    next();
  };
};

/**
 * Clear cache for a specific key or pattern
 * @param {string} keyPattern - Key or pattern to match keys
 * @returns {number} Number of keys deleted
 */
const clearCache = (keyPattern) => {
  if (!keyPattern) {
    return 0;
  }
  
  // If exact key, delete it
  if (cache.has(keyPattern)) {
    cache.del(keyPattern);
    return 1;
  }
  
  // If pattern, find matching keys and delete them
  const keys = cache.keys();
  const matchingKeys = keys.filter(key => key.includes(keyPattern));
  
  if (matchingKeys.length > 0) {
    cache.del(matchingKeys);
  }
  
  return matchingKeys.length;
};

/**
 * Clear all cache
 * @returns {void}
 */
const clearAllCache = () => {
  cache.flushAll();
};

/**
 * Get cache stats
 * @returns {Object} Cache statistics
 */
const getCacheStats = () => {
  return {
    keys: cache.keys().length,
    hits: cache.getStats().hits,
    misses: cache.getStats().misses,
    ksize: cache.getStats().ksize,
    vsize: cache.getStats().vsize
  };
};

/**
 * Set cache headers for HTTP response
 * @param {Object} res - Express response object
 * @param {number} maxAge - Max age in seconds
 */
const setCacheHeaders = (res, maxAge = 3600) => {
  // Set cache control headers
  res.set('Cache-Control', `public, max-age=${maxAge}`);
  
  // Set expires header
  const expiresDate = new Date(Date.now() + maxAge * 1000);
  res.set('Expires', expiresDate.toUTCString());
  
  // Set last modified header
  res.set('Last-Modified', new Date().toUTCString());
};

module.exports = {
  cache,
  cacheMiddleware,
  clearCache,
  clearAllCache,
  getCacheStats,
  setCacheHeaders
};
