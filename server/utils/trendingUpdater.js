const cron = require('node-cron');
const Product = require('../models/Product');
const TrendingCache = require('../models/trendingCache.model');
const { isConnected } = require('../config/db');
const mongoose = require('mongoose');

/**
 * Update trending products cache
 * This function is called by the cron job to update the trending products cache
 */
const updateTrendingCache = async () => {
  console.log('Updating trending products cache...');

  // Check if MongoDB is connected
  if (!isConnected()) {
    console.warn('MongoDB is not connected. Skipping trending products update.');
    return;
  }

  // Check if mongoose is ready
  if (mongoose.connection.readyState !== 1) {
    console.warn(`MongoDB connection not ready (state: ${mongoose.connection.readyState}). Skipping trending products update.`);
    return;
  }

  try {
    // Set a timeout for the operation
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Trending products update timed out after 20 seconds')), 20000);
    });

    // Get trending products by category for different time frames
    const trendingPromise = Product.getTrendingByCategory(null, 'week');

    // Race the promises
    const trendingByCategory = await Promise.race([trendingPromise, timeoutPromise]);

    // Update cache for different time frames
    await TrendingCache.findOneAndUpdate(
      { type: 'category', timeFrame: 'week' },
      { data: trendingByCategory, updatedAt: new Date() },
      { upsert: true, maxTimeMS: 10000 } // Set a 10-second timeout for this operation
    );

    console.log('Trending products cache updated successfully');
  } catch (error) {
    console.error('Error updating trending products cache:', error);

    // If it's a timeout error, log additional information
    if (error.message && error.message.includes('timed out')) {
      console.error('MongoDB operation timed out. This could be due to:');
      console.error('1. Slow database connection');
      console.error('2. Large dataset being processed');
      console.error('3. High server load');
      console.error('Connection state:', mongoose.connection.readyState);
    }
  }
};

/**
 * Schedule trending updates
 * This function schedules the trending updates to run every 4 hours
 */
const scheduleTrendingUpdates = () => {
  // Wait 30 seconds after startup before first run
  console.log('Scheduling trending products update in 30 seconds...');
  setTimeout(() => {
    updateTrendingCache().catch(err => {
      console.error('Failed to update trending products on startup:', err);
    });

    // Schedule to run every 4 hours
    cron.schedule('0 */4 * * *', () => {
      updateTrendingCache().catch(err => {
        console.error('Failed to update trending products on schedule:', err);
      });
    });
  }, 30000);
};

module.exports = { scheduleTrendingUpdates, updateTrendingCache };
