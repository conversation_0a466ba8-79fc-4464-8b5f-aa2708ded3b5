const express = require('express');
const { body } = require('express-validator');
const { login, getMe, registerAdmin } = require('../controllers/auth.controller');
const { protect } = require('../middleware/auth');
const { csrfProtection } = require('../middleware/security');

const router = express.Router();

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: User login
 *     description: Authenticates a user and returns a JWT token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               password:
 *                 type: string
 *                 format: password
 *                 description: User's password
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 token:
 *                   type: string
 *                   description: JWT token for authentication
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: User ID
 *                     name:
 *                       type: string
 *                       description: User's name
 *                     email:
 *                       type: string
 *                       description: User's email
 *                     role:
 *                       type: string
 *                       description: User's role (user or admin)
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Invalid credentials
 */
router.post(
  '/login',
  [
    body('email', 'Please include a valid email').isEmail(),
    body('password', 'Password is required').exists()
  ],
  login
);

/**
 * @swagger
 * /auth/me:
 *   get:
 *     summary: Get current user
 *     description: Returns the currently authenticated user's information
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: User ID
 *                     name:
 *                       type: string
 *                       description: User's name
 *                     email:
 *                       type: string
 *                       description: User's email
 *                     role:
 *                       type: string
 *                       description: User's role (user or admin)
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       description: Account creation date
 *       401:
 *         description: Not authorized to access this route
 */
router.get('/me', protect, getMe);

/**
 * @swagger
 * /auth/csrf-token:
 *   get:
 *     summary: Get CSRF token
 *     description: Returns a CSRF token for form submissions
 *     tags: [Authentication]
 *     responses:
 *       200:
 *         description: CSRF token retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 csrfToken:
 *                   type: string
 *                   description: CSRF token for form submissions
 */
router.get('/csrf-token', csrfProtection, (req, res) => {
  res.json({ success: true, csrfToken: req.csrfToken() });
});

// Register admin (development only)
if (process.env.NODE_ENV === 'development') {
  /**
   * @swagger
   * /auth/register-admin:
   *   post:
   *     summary: Register admin user (development only)
   *     description: Creates a new admin user (only available in development environment)
   *     tags: [Authentication]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - email
   *               - password
   *             properties:
   *               name:
   *                 type: string
   *                 description: Admin's name
   *               email:
   *                 type: string
   *                 format: email
   *                 description: Admin's email address
   *               password:
   *                 type: string
   *                 format: password
   *                 description: Admin's password (min 6 characters)
   *     responses:
   *       201:
   *         description: Admin user created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 token:
   *                   type: string
   *                   description: JWT token for authentication
   *                 user:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: User ID
   *                     name:
   *                       type: string
   *                       description: Admin's name
   *                     email:
   *                       type: string
   *                       description: Admin's email
   *                     role:
   *                       type: string
   *                       example: admin
   *                       description: User's role
   *       400:
   *         description: Invalid input or user already exists
   */
  router.post(
    '/register-admin',
    [
      body('name', 'Name is required').not().isEmpty(),
      body('email', 'Please include a valid email').isEmail(),
      body('password', 'Please enter a password with 6 or more characters').isLength({ min: 6 })
    ],
    registerAdmin
  );
}

module.exports = router;
