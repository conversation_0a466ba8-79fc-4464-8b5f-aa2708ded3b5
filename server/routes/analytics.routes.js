const express = require('express');
const { body } = require('express-validator');
const {
  trackProductClick,
  getProductClickCount,
  getTrendingByClicks
} = require('../controllers/productClick.controller');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /analytics/product-click:
 *   post:
 *     summary: Track a product click
 *     description: Record a click on a product for analytics
 *     tags: [Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - productId
 *             properties:
 *               productId:
 *                 type: string
 *                 description: ID of the clicked product
 *               sessionId:
 *                 type: string
 *                 description: Optional session ID for anonymous users
 *               referrer:
 *                 type: string
 *                 description: Optional referrer URL
 *     responses:
 *       200:
 *         description: Click tracked successfully
 *       400:
 *         description: Invalid input
 *       404:
 *         description: Product not found
 *       500:
 *         description: Server error
 */
router.post(
  '/product-click',
  [
    body('productId', 'Product ID is required').not().isEmpty()
  ],
  trackProductClick
);

/**
 * @swagger
 * /analytics/product-clicks/{id}:
 *   get:
 *     summary: Get click count for a product
 *     description: Get the number of clicks for a specific product (admin only)
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Click count retrieved successfully
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Not authorized as admin
 *       404:
 *         description: Product not found
 *       500:
 *         description: Server error
 */
router.get(
  '/product-clicks/:id',
  [protect, authorize('admin')],
  getProductClickCount
);

/**
 * @swagger
 * /analytics/trending-clicks:
 *   get:
 *     summary: Get trending products based on clicks
 *     description: Get products with the most clicks in a given time period (admin only)
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: Number of days to consider for trending calculation
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of products to return
 *     responses:
 *       200:
 *         description: Trending products retrieved successfully
 *       401:
 *         description: Not authorized
 *       403:
 *         description: Not authorized as admin
 *       500:
 *         description: Server error
 */
router.get(
  '/trending-clicks',
  [protect, authorize('admin')],
  getTrendingByClicks
);

module.exports = router;
