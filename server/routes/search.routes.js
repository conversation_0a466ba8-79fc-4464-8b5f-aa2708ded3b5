const express = require('express');
const { searchProducts, getSearchSuggestions } = require('../controllers/search.controller');
const { cacheMiddleware, setCacheHeaders } = require('../utils/cache');

const router = express.Router();

// Add cache headers middleware
const addCacheHeaders = (req, res, next) => {
  // Set cache headers with 5 minute expiry
  setCacheHeaders(res, 300);
  next();
};

/**
 * @swagger
 * tags:
 *   name: Search
 *   description: Product search API
 */

/**
 * @swagger
 * /search:
 *   get:
 *     summary: Search products
 *     description: Search for products by query, category, brand, and score range
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by product category
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: Filter by product brand
 *       - in: query
 *         name: minScore
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 100
 *         description: Minimum score filter
 *       - in: query
 *         name: maxScore
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 100
 *         description: Maximum score filter
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [textScore, score, newest, updated, name]
 *           default: textScore
 *         description: Sort order
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 count:
 *                   type: integer
 *                   description: Number of products returned
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       description: Current page
 *                     limit:
 *                       type: integer
 *                       description: Items per page
 *                     totalPages:
 *                       type: integer
 *                       description: Total number of pages
 *                     total:
 *                       type: integer
 *                       description: Total number of matching products
 *                     hasNextPage:
 *                       type: boolean
 *                       description: Whether there is a next page
 *                     hasPrevPage:
 *                       type: boolean
 *                       description: Whether there is a previous page
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: Product ID
 *                       name:
 *                         type: string
 *                         description: Product name
 *                       slug:
 *                         type: string
 *                         description: Product slug
 *                       brand:
 *                         type: string
 *                         description: Product brand
 *                       category:
 *                         type: string
 *                         description: Product category
 *                       image:
 *                         type: string
 *                         description: Product image URL
 *                       score:
 *                         type: number
 *                         description: Product score
 */
router.get('/', cacheMiddleware(300), addCacheHeaders, searchProducts);

/**
 * @swagger
 * /search/suggestions:
 *   get:
 *     summary: Get search suggestions
 *     description: Get search suggestions for autocomplete
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query
 *     responses:
 *       200:
 *         description: Search suggestions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                         enum: [product, brand, category]
 *                         description: Suggestion type
 *                       text:
 *                         type: string
 *                         description: Suggestion text
 *                       slug:
 *                         type: string
 *                         description: Product slug (only for product suggestions)
 */
router.get('/suggestions', cacheMiddleware(120), addCacheHeaders, getSearchSuggestions);

module.exports = router;
