{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["src/tempobook"], "references": [{"path": "./tsconfig.node.json"}]}