# Halo Review Admin Guide

Welcome to the Halo Review Admin Guide. This document provides detailed instructions for administrators on how to manage the Halo Review platform.

## Table of Contents

1. [Admin Access](#admin-access)
2. [Dashboard Overview](#dashboard-overview)
3. [Managing Products](#managing-products)
4. [User Management](#user-management)
5. [System Maintenance](#system-maintenance)
6. [Security Best Practices](#security-best-practices)

## Admin Access

### Logging In

1. Navigate to the admin login page at `/admin`
2. Enter your admin email and password
3. Click "Login"

### Session Management

- Your session will automatically expire after one week of inactivity
- To manually log out, click the "Logout" button in the top-right corner
- For security reasons, always log out when you're done

## Dashboard Overview

### Main Dashboard

The admin dashboard provides an overview of:
- Total number of products in the database
- Recently added products
- Most viewed products
- System status

### Navigation

The left sidebar contains links to:
- Products management
- User management
- Settings
- Logout

## Managing Products

### Adding a New Product

1. Click "Products" in the sidebar, then "Add New"
2. Fill in the required fields:
   - Name
   - Category (select from dropdown)
   - Brand
   - Image URL
   - Description
   - Pros (one per line)
   - Cons (one per line)
   - Opinion (editorial review)
   - Score (0-100)
3. Optional fields:
   - Affiliate Link
4. Click "Save" to add the product

### Editing a Product

1. Click "Products" in the sidebar
2. Find the product you want to edit and click "Edit"
3. Update the fields as needed
4. Click "Save" to update the product

### Deleting a Product

1. Click "Products" in the sidebar
2. Find the product you want to delete
3. Click "Delete"
4. Confirm the deletion in the popup

### Product Categories

Products must be assigned to one of the predefined categories:
- Electronics
- Computers & Accessories
- Smartphones & Accessories
- Audio & Headphones
- Cameras & Photography
- Wearable Technology
- Home & Kitchen
- Furniture
- Appliances
- Home Decor
- Bedding & Bath
- Clothing & Fashion
- Men's Clothing
- Women's Clothing
- Kids & Baby Clothing
- Shoes
- Jewelry & Watches
- Bags & Accessories
- Beauty & Personal Care
- Makeup
- Skincare
- Hair Care
- Fragrances
- Health & Wellness
- Sports & Outdoors
- Exercise & Fitness
- Outdoor Recreation
- Sports Equipment
- Toys & Games
- Books & Media
- Books
- Movies & TV
- Music
- Video Games
- Automotive
- Office Supplies
- Pet Supplies
- Food & Beverages
- Art & Crafts
- Tools & Home Improvement
- Garden & Outdoor
- Travel & Luggage
- Baby Products
- Musical Instruments
- Industrial & Scientific
- Other

## User Management

### Admin Users

Currently, the system only supports admin users. Regular user accounts are not implemented.

### Creating a New Admin (Development Only)

In development mode only:
1. Use the `/api/auth/register-admin` endpoint
2. Provide name, email, and password
3. The new user will be created with admin privileges

## System Maintenance

### Cache Management

The system implements caching for better performance:
- Product data is cached for 10 minutes
- Search results are cached for 5 minutes
- Search suggestions are cached for 2 minutes

To manually clear the cache:
1. Restart the server, or
2. Update a product (which automatically invalidates related caches)

### Database Indexes

The MongoDB database uses indexes for better performance. These are created automatically when the server starts.

## Security Best Practices

### Password Security

- Use strong, unique passwords (minimum 12 characters)
- Include uppercase letters, lowercase letters, numbers, and special characters
- Change your password regularly
- Never share your password with others

### CSRF Protection

The system implements CSRF protection:
- All forms include a CSRF token
- The token is automatically included in API requests
- Do not disable this protection

### Session Security

- Always log out when you're done
- Do not use admin access on public or shared computers
- Be aware that your session will expire after one week of inactivity

### Data Protection

- Only add accurate information to the database
- Double-check product details before saving
- Use appropriate scores based on objective criteria
- Ensure product images are appropriate and relevant

---

If you have any questions or need assistance, please contact the system administrator.
