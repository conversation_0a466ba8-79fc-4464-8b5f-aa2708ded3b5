# Halo Review Documentation

Welcome to the Halo Review documentation. This directory contains comprehensive documentation for users, administrators, and developers.

## Available Documentation

### User Documentation

- [User Guide](user-guide.md) - Guide for regular users of the Halo Review application

### Admin Documentation

- [Admin Guide](admin-guide.md) - Guide for administrators who manage the Halo Review platform

### Developer Documentation

- [Developer Guide](developer-guide.md) - Comprehensive documentation for developers who maintain and extend the application

## API Documentation

The API is documented using Swagger. When the server is running, you can access the Swagger UI at:

```
/api-docs
```

## Additional Resources

- [Security Documentation](../server/docs/SECURITY.md) - Details about the security measures implemented in the application
- [MongoDB Indexes](../server/utils/README_INDEXES.md) - Information about the MongoDB indexes used for performance optimization

## Getting Help

If you have any questions or need assistance, please contact the project maintainers.
