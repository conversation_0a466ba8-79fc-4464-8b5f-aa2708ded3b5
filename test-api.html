<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API</title>
</head>

<body>
    <h1>Test API</h1>
    <button id="testButton">Test API</button>
    <div id="result"></div>

    <script>
        document.getElementById('testButton').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Loading...';

            try {
                const response = await fetch('/api/search?q=headphones');
                console.log('Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Data:', data);

                resultDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        });
    </script>
</body>

</html>